# دليل نظام التحديث التلقائي - Auto-Updater Guide

## 📋 نظرة عامة - Overview

تم إضافة نظام تحديث تلقائي شامل لتطبيق "تصفية برو" باستخدام مكتبة `electron-updater`. يتيح هذا النظام للمستخدمين الحصول على أحدث الإصدارات تلقائياً مع واجهة عربية سهلة الاستخدام.

## 🔧 المكونات المضافة - Added Components

### 1. التبعيات الجديدة
- `electron-updater`: مكتبة التحديث التلقائي الرسمية لـ Electron

### 2. الملفات المضافة
- `src/updater-ui.js`: واجهة المستخدم لنظام التحديث
- `app-update.yml`: ملف إعدادات التحديث
- `test-updater.html`: ملف اختبار النظام
- `AUTO_UPDATER_GUIDE.md`: هذا الدليل

### 3. التعديلات على الملفات الموجودة
- `src/main.js`: إضافة كود التحديث التلقائي
- `src/app.js`: إضافة API ودوال التحديث
- `src/index.html`: إضافة تبويب التحديثات
- `package.json`: إضافة إعدادات النشر

## 🚀 كيفية الاستخدام - How to Use

### للمستخدمين:

1. **فحص التحديثات يدوياً:**
   - اذهب إلى الإعدادات → التحديثات
   - اضغط على "البحث عن تحديثات"

2. **تفعيل التحديث التلقائي:**
   - في تبويب التحديثات، فعل خيار "تفعيل التحديث التلقائي"
   - سيقوم التطبيق بالبحث عن التحديثات عند كل بدء تشغيل

3. **تنزيل وتثبيت التحديثات:**
   - عند توفر تحديث، ستظهر رسالة إشعار
   - اضغط "تنزيل التحديث" ثم "تثبيت وإعادة التشغيل"

### للمطورين:

1. **إعداد مستودع GitHub:**
   ```bash
   # إنشاء مستودع جديد على GitHub
   # تحديث package.json بمعلومات المستودع
   ```

2. **بناء ونشر إصدار جديد:**
   ```bash
   # زيادة رقم الإصدار
   npm version patch  # أو minor أو major
   
   # بناء التطبيق
   npm run build
   
   # نشر على GitHub Releases
   # (يتم تلقائياً مع electron-builder)
   ```

## ⚙️ الإعدادات - Configuration

### إعدادات package.json:
```json
{
  "build": {
    "publish": {
      "provider": "github",
      "owner": "your-github-username",
      "repo": "tasfiya-pro",
      "private": false
    }
  }
}
```

### إعدادات app-update.yml:
```yaml
provider: github
owner: your-github-username
repo: tasfiya-pro
updaterCacheDirName: tasfiya-pro-updater
```

## 🔍 اختبار النظام - Testing

1. **تشغيل اختبارات النظام:**
   - افتح `test-updater.html` في المتصفح
   - اضغط "تشغيل جميع الاختبارات"

2. **اختبار في بيئة التطوير:**
   ```bash
   npm run dev
   # النظام سيتخطى فحص التحديثات في وضع التطوير
   ```

3. **اختبار في بيئة الإنتاج:**
   ```bash
   npm run build
   # تشغيل الملف المبني واختبار التحديثات
   ```

## 🛠️ استكشاف الأخطاء - Troubleshooting

### مشاكل شائعة:

1. **"فحص التحديثات غير متاح في وضع التطوير"**
   - هذا طبيعي، النظام يعمل فقط في الإصدارات المبنية

2. **"حدث خطأ أثناء البحث عن التحديثات"**
   - تحقق من اتصال الإنترنت
   - تحقق من إعدادات GitHub في package.json

3. **التحديث لا يظهر**
   - تأكد من رفع إصدار جديد على GitHub Releases
   - تحقق من رقم الإصدار في package.json

## 📝 ملاحظات مهمة - Important Notes

1. **الأمان:** النظام يتحقق من التوقيعات الرقمية للتحديثات
2. **الشبكة:** يتطلب اتصال إنترنت للبحث عن التحديثات
3. **التخزين:** التحديثات تُحفظ مؤقتاً في مجلد النظام
4. **الصلاحيات:** قد يتطلب صلاحيات إدارية لتثبيت التحديثات

## 🔄 دورة حياة التحديث - Update Lifecycle

1. **فحص التحديثات** → 2. **تنزيل التحديث** → 3. **تثبيت التحديث** → 4. **إعادة تشغيل التطبيق**

## 📞 الدعم - Support

للمساعدة أو الإبلاغ عن مشاكل:
- تحقق من سجلات التطبيق (Console)
- راجع ملف `test-updater.html` للتشخيص
- تواصل مع فريق التطوير

---

**تم إنشاء هذا النظام بواسطة:** محمد أمين الكامل  
**التاريخ:** 2025-01-23  
**الإصدار:** 2.0.0
