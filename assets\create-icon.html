<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء أيقونة تصفية برو</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        body {
            font-family: 'Cairo', Arial, sans-serif;
            direction: rtl;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .icon-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            margin-bottom: 30px;
        }
        
        .icon-canvas {
            width: 256px;
            height: 256px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            margin: 0 auto;
        }
        
        .download-section {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            color: white;
        }
        
        .download-btn {
            background: #48bb78;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 14px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            background: #38a169;
            transform: translateY(-2px);
        }
        
        .instructions {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            color: white;
            margin-top: 20px;
            text-align: right;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="icon-container">
        <h1 style="color: #2d3748; margin-bottom: 20px;">أيقونة تصفية برو</h1>
        <canvas id="iconCanvas" class="icon-canvas" width="256" height="256"></canvas>
    </div>
    
    <div class="download-section">
        <h2>تحميل الأيقونة</h2>
        <button class="download-btn" onclick="downloadIcon('png')">تحميل PNG</button>
        <button class="download-btn" onclick="downloadIcon('ico')">تحميل ICO</button>
        <button class="download-btn" onclick="downloadIcon('svg')">تحميل SVG</button>
    </div>
    
    <div class="instructions">
        <h3>تعليمات التثبيت:</h3>
        <div class="step">
            <strong>1. تحميل الأيقونة:</strong> اضغط على "تحميل ICO" للحصول على ملف الأيقونة
        </div>
        <div class="step">
            <strong>2. نسخ الملف:</strong> انسخ الملف المحمل إلى مجلد assets في مشروع التطبيق
        </div>
        <div class="step">
            <strong>3. إعادة تسمية:</strong> أعد تسمية الملف إلى "icon.ico"
        </div>
        <div class="step">
            <strong>4. إعادة التشغيل:</strong> أعد تشغيل التطبيق لرؤية الأيقونة الجديدة
        </div>
    </div>

    <script>
        // رسم الأيقونة على Canvas
        function drawIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // خلفية متدرجة
            const gradient = ctx.createLinearGradient(0, 0, 256, 256);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(0.5, '#764ba2');
            gradient.addColorStop(1, '#5a67d8');
            
            // رسم الخلفية الدائرية
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(128, 128, 120, 0, 2 * Math.PI);
            ctx.fill();
            
            // حدود داخلية
            ctx.strokeStyle = 'rgba(255,255,255,0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(128, 128, 110, 0, 2 * Math.PI);
            ctx.stroke();
            
            // بطاقة ائتمان رئيسية
            ctx.fillStyle = 'rgba(255,255,255,0.95)';
            ctx.beginPath();
            ctx.roundRect(60, 90, 136, 85, 12);
            ctx.fill();
            
            ctx.strokeStyle = '#e2e8f0';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // شريط مغناطيسي
            ctx.fillStyle = '#2d3748';
            ctx.beginPath();
            ctx.roundRect(70, 105, 116, 12, 2);
            ctx.fill();
            
            // رقم البطاقة (مبسط)
            ctx.fillStyle = '#4a5568';
            for (let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.roundRect(75 + i * 12, 130, 8, 6, 1);
                ctx.fill();
            }
            
            for (let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.roundRect(130 + i * 12, 130, 8, 6, 1);
                ctx.fill();
            }
            
            // شعار الشركة
            const logoGradient = ctx.createLinearGradient(158, 143, 182, 167);
            logoGradient.addColorStop(0, '#3182ce');
            logoGradient.addColorStop(1, '#2c5282');
            
            ctx.fillStyle = logoGradient;
            ctx.beginPath();
            ctx.arc(170, 155, 12, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.arc(170, 155, 8, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#3182ce';
            ctx.font = 'bold 10px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('ت', 170, 160);
            
            // أيقونة ATM
            ctx.fillStyle = logoGradient;
            ctx.beginPath();
            ctx.roundRect(40, 50, 35, 45, 4);
            ctx.fill();
            
            // شاشة ATM
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.roundRect(45, 55, 25, 18, 2);
            ctx.fill();
            
            ctx.fillStyle = '#1a202c';
            ctx.beginPath();
            ctx.roundRect(47, 57, 21, 14, 1);
            ctx.fill();
            
            ctx.fillStyle = '#48bb78';
            ctx.beginPath();
            ctx.roundRect(49, 59, 17, 10, 1);
            ctx.fill();
            
            ctx.fillStyle = '#ffffff';
            ctx.font = '6px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('$', 57.5, 66);
            
            // لوحة المفاتيح
            ctx.fillStyle = '#e2e8f0';
            for (let row = 0; row < 2; row++) {
                for (let col = 0; col < 4; col++) {
                    ctx.beginPath();
                    ctx.roundRect(47 + col * 6, 77 + row * 5, 4, 3, 0.5);
                    ctx.fill();
                }
            }
            
            // أيقونة تقرير
            ctx.fillStyle = '#ffffff';
            ctx.beginPath();
            ctx.roundRect(180, 50, 30, 40, 3);
            ctx.fill();
            
            ctx.strokeStyle = '#e2e8f0';
            ctx.lineWidth = 1;
            ctx.stroke();
            
            // خطوط التقرير
            ctx.strokeStyle = '#4a5568';
            ctx.lineWidth = 1.5;
            ctx.beginPath();
            ctx.moveTo(185, 60);
            ctx.lineTo(205, 60);
            ctx.stroke();
            
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(185, 65);
            ctx.lineTo(200, 65);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(185, 70);
            ctx.lineTo(205, 70);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(185, 75);
            ctx.lineTo(195, 75);
            ctx.stroke();
            
            // رسم بياني صغير
            const colors = ['#48bb78', '#4299e1', '#ed8936', '#9f7aea'];
            const heights = [6, 8, 4, 10];
            for (let i = 0; i < 4; i++) {
                ctx.fillStyle = colors[i];
                ctx.beginPath();
                ctx.roundRect(185 + i * 5, 86 - heights[i], 3, heights[i], 1);
                ctx.fill();
            }
            
            // نص "تصفية برو"
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('تصفية برو', 128, 210);
            
            ctx.font = '10px Arial';
            ctx.globalAlpha = 0.8;
            ctx.fillText('Tasfiya Pro', 128, 230);
            ctx.globalAlpha = 1;
        }
        
        // تحميل الأيقونة
        function downloadIcon(format) {
            const canvas = document.getElementById('iconCanvas');
            
            if (format === 'png') {
                const link = document.createElement('a');
                link.download = 'tasfiya-pro-icon.png';
                link.href = canvas.toDataURL('image/png');
                link.click();
            } else if (format === 'ico') {
                // تحويل إلى ICO (مبسط)
                const link = document.createElement('a');
                link.download = 'tasfiya-pro-icon.ico';
                link.href = canvas.toDataURL('image/png');
                link.click();
                alert('تم تحميل الملف كـ PNG. يمكنك استخدام أداة تحويل عبر الإنترنت لتحويله إلى ICO');
            } else if (format === 'svg') {
                // تحميل ملف SVG
                const svgContent = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
  <!-- محتوى SVG هنا -->
</svg>`;
                const blob = new Blob([svgContent], { type: 'image/svg+xml' });
                const link = document.createElement('a');
                link.download = 'tasfiya-pro-icon.svg';
                link.href = URL.createObjectURL(blob);
                link.click();
            }
        }
        
        // رسم الأيقونة عند تحميل الصفحة
        window.onload = function() {
            drawIcon();
        };
    </script>
</body>
</html>
