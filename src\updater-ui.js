// ===================================================
// 🧾 تطبيق: تصفية برو
// 🛠️ المطور: محمد أمين الكامل
// 🗓️ سنة: 2025
// 📌 جميع الحقوق محفوظة
// يمنع الاستخدام أو التعديل دون إذن كتابي
// ===================================================

/**
 * نظام واجهة المستخدم للتحديثات التلقائية
 * Auto-Updater UI System
 */

class UpdaterUI {
    constructor() {
        this.isUpdateAvailable = false;
        this.isDownloading = false;
        this.isUpdateReady = false;
        this.currentVersion = '';
        this.newVersion = '';
        this.downloadProgress = 0;
        
        this.init();
    }

    /**
     * تهيئة نظام واجهة التحديثات
     */
    init() {
        console.log('🔄 [UPDATER-UI] Initializing updater UI...');
        
        // Listen for update events from main process
        if (window.electronAPI) {
            window.electronAPI.onUpdateStatus(this.handleUpdateStatus.bind(this));
        }
        
        // Get current version
        this.getCurrentVersion();
        
        // Create update UI elements
        this.createUpdateUI();
        
        console.log('✅ [UPDATER-UI] Updater UI initialized');
    }

    /**
     * الحصول على الإصدار الحالي
     */
    async getCurrentVersion() {
        try {
            const result = await window.electronAPI.getAppVersion();
            if (result.success) {
                this.currentVersion = result.version;
                console.log('ℹ️ [UPDATER-UI] Current version:', this.currentVersion);
            }
        } catch (error) {
            console.error('❌ [UPDATER-UI] Error getting current version:', error);
        }
    }

    /**
     * إنشاء عناصر واجهة التحديث
     */
    createUpdateUI() {
        // Create update notification container
        const updateContainer = document.createElement('div');
        updateContainer.id = 'update-notification';
        updateContainer.className = 'update-notification d-none';
        updateContainer.innerHTML = `
            <div class="update-content">
                <div class="update-header">
                    <i class="fas fa-download text-primary"></i>
                    <span class="update-title">تحديث التطبيق</span>
                    <button type="button" class="btn-close" onclick="updaterUI.hideUpdateNotification()"></button>
                </div>
                <div class="update-body">
                    <div class="update-message" id="update-message">جاري البحث عن التحديثات...</div>
                    <div class="update-progress d-none" id="update-progress">
                        <div class="progress mb-2">
                            <div class="progress-bar" id="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="progress-text">0%</small>
                    </div>
                    <div class="update-details d-none" id="update-details">
                        <small class="text-muted">
                            الإصدار الحالي: <span id="current-version">${this.currentVersion}</span><br>
                            الإصدار الجديد: <span id="new-version">-</span>
                        </small>
                    </div>
                </div>
                <div class="update-actions" id="update-actions">
                    <button class="btn btn-primary btn-sm" id="check-update-btn" onclick="updaterUI.checkForUpdates()">
                        <i class="fas fa-search"></i> البحث عن تحديثات
                    </button>
                    <button class="btn btn-success btn-sm d-none" id="download-update-btn" onclick="updaterUI.downloadUpdate()">
                        <i class="fas fa-download"></i> تنزيل التحديث
                    </button>
                    <button class="btn btn-warning btn-sm d-none" id="install-update-btn" onclick="updaterUI.installUpdate()">
                        <i class="fas fa-sync-alt"></i> تثبيت وإعادة التشغيل
                    </button>
                </div>
            </div>
        `;

        // Add to page
        document.body.appendChild(updateContainer);

        // Add CSS styles
        this.addUpdateStyles();
    }

    /**
     * إضافة أنماط CSS للتحديثات
     */
    addUpdateStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .update-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 350px;
                background: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                direction: rtl;
                text-align: right;
            }

            .update-content {
                padding: 15px;
            }

            .update-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 10px;
                padding-bottom: 8px;
                border-bottom: 1px solid #dee2e6;
            }

            .update-title {
                font-weight: 600;
                color: #2c3e50;
                margin-right: 8px;
            }

            .update-message {
                margin-bottom: 10px;
                color: #495057;
                font-size: 14px;
            }

            .update-actions {
                margin-top: 10px;
                display: flex;
                gap: 8px;
                justify-content: flex-end;
            }

            .update-actions .btn {
                font-size: 12px;
                padding: 6px 12px;
            }

            .btn-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #6c757d;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .btn-close:hover {
                color: #495057;
            }

            .btn-close::before {
                content: '×';
            }

            .update-notification.slide-in {
                animation: slideIn 0.3s ease-out;
            }

            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * معالجة أحداث التحديث من العملية الرئيسية
     */
    handleUpdateStatus(updateInfo) {
        const { event, data } = updateInfo;
        console.log('🔄 [UPDATER-UI] Update status:', event, data);

        switch (event) {
            case 'checking-for-update':
                this.showUpdateNotification();
                this.setUpdateMessage(data.message, 'info');
                this.showButton('check-update-btn', false);
                break;

            case 'update-available':
                this.isUpdateAvailable = true;
                this.newVersion = data.version;
                this.setUpdateMessage(data.message, 'success');
                this.showUpdateDetails();
                this.showButton('download-update-btn', true);
                this.showButton('check-update-btn', false);
                break;

            case 'update-not-available':
                this.setUpdateMessage(data.message, 'info');
                this.showButton('check-update-btn', true);
                setTimeout(() => this.hideUpdateNotification(), 3000);
                break;

            case 'download-progress':
                this.isDownloading = true;
                this.downloadProgress = data.percent;
                this.setUpdateMessage(data.message, 'info');
                this.showProgress(data.percent);
                this.showButton('download-update-btn', false);
                break;

            case 'update-downloaded':
                this.isUpdateReady = true;
                this.isDownloading = false;
                this.setUpdateMessage(data.message, 'success');
                this.hideProgress();
                this.showButton('install-update-btn', true);
                break;

            case 'update-error':
                this.setUpdateMessage(data.message, 'error');
                this.showButton('check-update-btn', true);
                this.hideProgress();
                break;
        }
    }

    /**
     * عرض إشعار التحديث
     */
    showUpdateNotification() {
        const notification = document.getElementById('update-notification');
        if (notification) {
            notification.classList.remove('d-none');
            notification.classList.add('slide-in');
        }
    }

    /**
     * إخفاء إشعار التحديث
     */
    hideUpdateNotification() {
        const notification = document.getElementById('update-notification');
        if (notification) {
            notification.classList.add('d-none');
            notification.classList.remove('slide-in');
        }
    }

    /**
     * تعيين رسالة التحديث
     */
    setUpdateMessage(message, type = 'info') {
        const messageEl = document.getElementById('update-message');
        if (messageEl) {
            messageEl.textContent = message;
            messageEl.className = `update-message text-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'}`;
        }
    }

    /**
     * عرض تفاصيل التحديث
     */
    showUpdateDetails() {
        const detailsEl = document.getElementById('update-details');
        const newVersionEl = document.getElementById('new-version');
        
        if (detailsEl) detailsEl.classList.remove('d-none');
        if (newVersionEl) newVersionEl.textContent = this.newVersion;
    }

    /**
     * عرض شريط التقدم
     */
    showProgress(percent) {
        const progressContainer = document.getElementById('update-progress');
        const progressBar = document.getElementById('progress-bar');
        const progressText = document.getElementById('progress-text');

        if (progressContainer) progressContainer.classList.remove('d-none');
        if (progressBar) progressBar.style.width = `${percent}%`;
        if (progressText) progressText.textContent = `${percent}%`;
    }

    /**
     * إخفاء شريط التقدم
     */
    hideProgress() {
        const progressContainer = document.getElementById('update-progress');
        if (progressContainer) progressContainer.classList.add('d-none');
    }

    /**
     * عرض/إخفاء الأزرار
     */
    showButton(buttonId, show) {
        const button = document.getElementById(buttonId);
        if (button) {
            if (show) {
                button.classList.remove('d-none');
            } else {
                button.classList.add('d-none');
            }
        }
    }

    /**
     * البحث عن التحديثات يدوياً
     */
    async checkForUpdates() {
        console.log('🔍 [UPDATER-UI] Manual update check requested');
        try {
            this.showUpdateNotification();
            this.setUpdateMessage('جاري البحث عن التحديثات...', 'info');
            this.showButton('check-update-btn', false);

            const result = await window.electronAPI.checkForUpdates();
            
            if (!result.success) {
                if (result.isDevelopment) {
                    this.setUpdateMessage(result.message, 'info');
                } else {
                    this.setUpdateMessage('حدث خطأ أثناء البحث عن التحديثات', 'error');
                }
                this.showButton('check-update-btn', true);
            }
        } catch (error) {
            console.error('❌ [UPDATER-UI] Error checking for updates:', error);
            this.setUpdateMessage('حدث خطأ أثناء البحث عن التحديثات', 'error');
            this.showButton('check-update-btn', true);
        }
    }

    /**
     * تنزيل التحديث
     */
    async downloadUpdate() {
        console.log('📥 [UPDATER-UI] Download update requested');
        try {
            this.setUpdateMessage('جاري تنزيل التحديث...', 'info');
            this.showButton('download-update-btn', false);

            const result = await window.electronAPI.downloadUpdate();
            
            if (!result.success) {
                this.setUpdateMessage('حدث خطأ أثناء تنزيل التحديث', 'error');
                this.showButton('download-update-btn', true);
            }
        } catch (error) {
            console.error('❌ [UPDATER-UI] Error downloading update:', error);
            this.setUpdateMessage('حدث خطأ أثناء تنزيل التحديث', 'error');
            this.showButton('download-update-btn', true);
        }
    }

    /**
     * تثبيت التحديث وإعادة التشغيل
     */
    async installUpdate() {
        console.log('🔄 [UPDATER-UI] Install update requested');
        
        // Show confirmation dialog
        const confirmed = await Swal.fire({
            title: 'تثبيت التحديث',
            text: 'سيتم إغلاق التطبيق وتثبيت التحديث. هل تريد المتابعة؟',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'نعم، ثبت التحديث',
            cancelButtonText: 'إلغاء',
            confirmButtonColor: '#28a745',
            cancelButtonColor: '#6c757d'
        });

        if (confirmed.isConfirmed) {
            try {
                this.setUpdateMessage('جاري تثبيت التحديث...', 'info');
                this.showButton('install-update-btn', false);

                const result = await window.electronAPI.installUpdate();
                
                if (!result.success) {
                    this.setUpdateMessage('حدث خطأ أثناء تثبيت التحديث', 'error');
                    this.showButton('install-update-btn', true);
                }
            } catch (error) {
                console.error('❌ [UPDATER-UI] Error installing update:', error);
                this.setUpdateMessage('حدث خطأ أثناء تثبيت التحديث', 'error');
                this.showButton('install-update-btn', true);
            }
        }
    }
}

// Initialize updater UI when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.updaterUI = new UpdaterUI();
});
