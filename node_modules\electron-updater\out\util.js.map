{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;AAMA,gCAMC;AAID,wCAUC;AAED,gDAEC;AAED,sCAIC;AApCD,mHAAmH;AACnH,6BAAyB;AACzB,aAAa;AACb,oDAAmD;AAEnD,gBAAgB;AAChB,SAAgB,UAAU,CAAC,GAAW;IACpC,MAAM,MAAM,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,CAAA;IAC3B,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACnC,MAAM,CAAC,QAAQ,IAAI,GAAG,CAAA;IACxB,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,8GAA8G;AAC9G,iEAAiE;AACjE,SAAgB,cAAc,CAAC,QAAgB,EAAE,OAAY,EAAE,4BAA4B,GAAG,KAAK;IACjG,MAAM,MAAM,GAAG,IAAI,SAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACzC,wEAAwE;IACxE,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAA;IAC7B,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1C,MAAM,CAAC,MAAM,GAAG,MAAM,CAAA;IACxB,CAAC;SAAM,IAAI,4BAA4B,EAAE,CAAC;QACxC,MAAM,CAAC,MAAM,GAAG,WAAW,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAA;IACtD,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAgB,kBAAkB,CAAC,OAAe;IAChD,OAAO,GAAG,OAAO,MAAM,CAAA;AACzB,CAAC;AAED,SAAgB,aAAa,CAAC,OAAY,EAAE,UAAkB,EAAE,UAAkB;IAChF,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,OAAO,CAAC,QAAQ,WAAW,EAAE,OAAO,CAAC,CAAA;IAC9E,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,EAAE,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IAC7I,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,CAAA;AACzC,CAAC", "sourcesContent": ["// if baseUrl path doesn't ends with /, this path will be not prepended to passed pathname for new URL(input, base)\nimport { URL } from \"url\"\n// @ts-ignore\nimport * as escapeRegExp from \"lodash.escaperegexp\"\n\n/** @internal */\nexport function newBaseUrl(url: string): URL {\n  const result = new URL(url)\n  if (!result.pathname.endsWith(\"/\")) {\n    result.pathname += \"/\"\n  }\n  return result\n}\n\n// addRandomQueryToAvoidCaching is false by default because in most cases URL already contains version number,\n// so, it makes sense only for Generic Provider for channel files\nexport function newUrlFromBase(pathname: string, baseUrl: URL, addRandomQueryToAvoidCaching = false): URL {\n  const result = new URL(pathname, baseUrl)\n  // search is not propagated (search is an empty string if not specified)\n  const search = baseUrl.search\n  if (search != null && search.length !== 0) {\n    result.search = search\n  } else if (addRandomQueryToAvoidCaching) {\n    result.search = `noCache=${Date.now().toString(32)}`\n  }\n  return result\n}\n\nexport function getChannelFilename(channel: string): string {\n  return `${channel}.yml`\n}\n\nexport function blockmapFiles(baseUrl: URL, oldVersion: string, newVersion: string): URL[] {\n  const newBlockMapUrl = newUrlFromBase(`${baseUrl.pathname}.blockmap`, baseUrl)\n  const oldBlockMapUrl = newUrlFromBase(`${baseUrl.pathname.replace(new RegExp(escapeRegExp(newVersion), \"g\"), oldVersion)}.blockmap`, baseUrl)\n  return [oldBlockMapUrl, newBlockMapUrl]\n}\n"]}