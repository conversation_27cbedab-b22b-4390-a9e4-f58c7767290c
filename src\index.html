<!--
===================================================
🧾 تطبيق: تصفية برو
🛠️ المطور: محمد أمين الكامل
🗓️ سنة: 2025
📌 جميع الحقوق محفوظة
يمنع الاستخدام أو التعديل دون إذن كتابي
===================================================
-->
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تصفية برو</title>
    
    <!-- Bootstrap CSS -->
    <link href="../node_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- SweetAlert2 CSS -->
    <link href="../node_modules/sweetalert2/dist/sweetalert2.min.css" rel="stylesheet">

    <!-- Google Fonts for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h2 class="text-center mb-4">تصفية برو</h2>
                <p class="text-center text-muted">تسجيل الدخول للمديرين</p>
            </div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label">اسم المستخدم</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">كلمة المرور</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-lg">دخول</button>
                </div>
                
                <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
            </form>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" style="display: none;">
        <!-- Navigation Sidebar -->
        <nav id="sidebar" class="sidebar">
            <!-- Sidebar Toggle Button -->
            <button id="sidebarToggle" class="sidebar-toggle-btn"
                    title="إظهار/إخفاء القائمة الجانبية (Ctrl+B)"
                    aria-label="تبديل القائمة الجانبية"
                    role="button">
                <i class="toggle-icon" aria-hidden="true"></i>
            </button>

            <div class="sidebar-header">
                <h4>تصفية برو</h4>
                <div class="user-info">
                    <span id="currentUser"></span>
                    <button id="logoutBtn" class="btn btn-sm btn-outline-light">خروج</button>
                </div>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="#" data-section="reconciliation" class="menu-item active">
                    <i class="icon">💰</i> التصفية الجديدة
                </a></li>
                <li><a href="#" data-section="branches" class="menu-item">
                    <i class="icon">🏢</i> إدارة الفروع
                </a></li>
                <li><a href="#" data-section="cashiers" class="menu-item">
                    <i class="icon">👤</i> إدارة الكاشير
                </a></li>
                <li><a href="#" data-section="admins" class="menu-item">
                    <i class="icon">🧑‍💼</i> إدارة المسؤولين
                </a></li>
                <li><a href="#" data-section="accountants" class="menu-item">
                    <i class="icon">👨‍💼</i> إدارة المحاسبين
                </a></li>
                <li><a href="#" data-section="atms" class="menu-item">
                    <i class="icon">🏧</i> إدارة الآلات
                </a></li>
                <li><a href="#" data-section="saved-reconciliations" class="menu-item">
                    <i class="icon">💾</i> التصفيات المحفوظة
                </a></li>
                <li><a href="#" data-section="reports" class="menu-item">
                    <i class="icon">📄</i> تقارير التصفيات
                </a></li>
                <li><a href="#" data-section="advanced-reports" class="menu-item">
                    <i class="icon">📈</i> تقارير متقدمة
                </a></li>
                <li><a href="#" data-section="cashier-performance" class="menu-item">
                    <i class="icon">🏆</i> مقارنة أداء الكاشير
                </a></li>
                <li><a href="#" data-section="settings" class="menu-item">
                    <i class="icon">⚙️</i> الإعدادات
                </a></li>
            </ul>
        </nav>

        <!-- Fixed Toggle Button (appears when sidebar is collapsed) -->
        <button id="fixedSidebarToggle" class="fixed-sidebar-toggle-btn"
                title="إظهار القائمة الجانبية (Ctrl+B)"
                aria-label="إظهار القائمة الجانبية"
                role="button"
                style="display: none;">
            <i class="toggle-icon" aria-hidden="true"></i>
        </button>

        <!-- Main Content -->
        <main id="mainContent" class="main-content">
            <!-- Edit Reconciliation Modal -->
            <div class="modal fade" id="editReconciliationModal" tabindex="-1" aria-labelledby="editReconciliationModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title" id="editReconciliationModalLabel">
                                <i class="icon">✏️</i> تعديل التصفية
                                <span class="badge bg-secondary ms-2" id="editProgressBadge">0/6 مكتمل</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Edit Mode Alert -->
                            <div class="alert alert-warning d-flex align-items-center mb-4" role="alert">
                                <i class="icon me-2">⚠️</i>
                                <div class="flex-grow-1">
                                    <strong>وضع التعديل:</strong> تقوم بتعديل التصفية رقم <span id="editReconciliationId">#</span>
                                    <br>
                                    <small>تاريخ الإنشاء: <span id="editCreatedDate"></span> | آخر تعديل: <span id="editLastModified"></span></small>
                                </div>
                            </div>

                            <!-- Edit Reconciliation Form -->
                            <form id="editReconciliationForm">
                                <!-- Cashier and Accountant Info -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">معلومات التصفية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-3">
                                                <label for="editCashierSelect" class="form-label">اسم الكاشير</label>
                                                <select class="form-select" id="editCashierSelect" required>
                                                    <option value="">اختر الكاشير</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="editCashierNumber" class="form-label">رقم الكاشير</label>
                                                <input type="text" class="form-control" id="editCashierNumber" readonly>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="editAccountantSelect" class="form-label">اسم المحاسب</label>
                                                <select class="form-select" id="editAccountantSelect" required>
                                                    <option value="">اختر المحاسب</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="editReconciliationDate" class="form-label">تاريخ التصفية</label>
                                                <input type="date" class="form-control" id="editReconciliationDate" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Bank Receipts Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">المقبوضات البنكية</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditBankReceipt()">
                                            <i class="icon">➕</i> إضافة مقبوضة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>نوع العملية</th>
                                                        <th>الجهاز</th>
                                                        <th>البنك</th>
                                                        <th>المبلغ</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editBankReceiptsTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong>إجمالي المقبوضات البنكية: <span id="editBankReceiptsTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cash Receipts Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">المقبوضات النقدية</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditCashReceipt()">
                                            <i class="icon">➕</i> إضافة فئة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>الفئة</th>
                                                        <th>العدد</th>
                                                        <th>المجموع</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editCashReceiptsTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong>إجمالي المقبوضات النقدية: <span id="editCashReceiptsTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Postpaid Sales Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">المبيعات الآجلة</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditPostpaidSale()">
                                            <i class="icon">➕</i> إضافة مبيعة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>اسم العميل</th>
                                                        <th>المبلغ</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editPostpaidSalesTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong>إجمالي المبيعات الآجلة: <span id="editPostpaidSalesTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Receipts Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">مقبوضات العملاء</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditCustomerReceipt()">
                                            <i class="icon">➕</i> إضافة مقبوضة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>اسم العميل</th>
                                                        <th>المبلغ</th>
                                                        <th>نوع الدفع</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editCustomerReceiptsTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong>إجمالي مقبوضات العملاء: <span id="editCustomerReceiptsTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Return Invoices Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">فواتير المرتجعات</h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditReturnInvoice()">
                                            <i class="icon">➕</i> إضافة فاتورة
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>رقم الفاتورة</th>
                                                        <th>المبلغ</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editReturnInvoicesTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong>إجمالي فواتير المرتجعات: <span id="editReturnInvoicesTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Suppliers Section -->
                                <div class="card mb-4 edit-modal-section">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h6 class="mb-0">الموردين <small class="text-muted">(للعرض فقط - لا يؤثر على المجاميع)</small></h6>
                                        <button type="button" class="btn btn-sm btn-primary" onclick="addEditSupplier()">
                                            <i class="icon">➕</i> إضافة مورد
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped edit-table">
                                                <thead>
                                                    <tr>
                                                        <th>اسم المورد</th>
                                                        <th>المبلغ</th>
                                                        <th>الإجراءات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="editSuppliersTable">
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="text-end">
                                            <strong class="text-warning">إجمالي الموردين (للعرض فقط): <span id="editSuppliersTotal">0.00</span> ريال</strong>
                                        </div>
                                    </div>
                                </div>

                                <!-- Summary Section -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h6 class="mb-0">ملخص التصفية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row g-3">
                                            <div class="col-md-4">
                                                <label for="editSystemSales" class="form-label">مبيعات النظام</label>
                                                <input type="number" class="form-control" id="editSystemSales" step="0.01" min="0" required>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">إجمالي المقبوضات</label>
                                                <div class="form-control-plaintext fw-bold" id="editTotalReceipts">0.00 ريال</div>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الفائض/العجز</label>
                                                <div class="form-control-plaintext fw-bold" id="editSurplusDeficit">0.00 ريال</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="saveEditedReconciliation()">
                                <i class="icon">💾</i> حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Bank Receipt Modal -->
            <div class="modal fade" id="addEditBankReceiptModal" tabindex="-1" aria-labelledby="addEditBankReceiptModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="addEditBankReceiptModalLabel">
                                <i class="icon">🏦</i> <span id="bankReceiptModalTitle">إضافة مقبوضة بنكية</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="bankReceiptEditForm">
                                <div class="mb-3">
                                    <label for="editOperationType" class="form-label">نوع العملية</label>
                                    <select class="form-select" id="editOperationType" required>
                                        <option value="">اختر نوع العملية</option>
                                        <option value="مدى">مدى</option>
                                        <option value="فيزا">فيزا</option>
                                        <option value="ماستر كارد">ماستر كارد</option>
                                        <option value="أمريكان إكسبريس">أمريكان إكسبريس</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editAtmSelect" class="form-label">الجهاز</label>
                                    <select class="form-select" id="editAtmSelect" required>
                                        <option value="">اختر الجهاز</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editBankName" class="form-label">اسم البنك</label>
                                    <input type="text" class="form-control" id="editBankName" readonly>
                                </div>
                                <div class="mb-3">
                                    <label for="bankReceiptAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control amount-input" id="bankReceiptAmount" step="0.01" min="0" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="saveBankReceiptEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Cash Receipt Modal -->
            <div class="modal fade" id="addEditCashReceiptModal" tabindex="-1" aria-labelledby="addEditCashReceiptModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="addEditCashReceiptModalLabel">
                                <i class="icon">💵</i> <span id="cashReceiptModalTitle">إضافة فئة نقدية</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="cashReceiptEditForm">
                                <div class="mb-3">
                                    <label for="editDenomination" class="form-label">فئة الورقة</label>
                                    <select class="form-select" id="editDenomination" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="500">500 ريال</option>
                                        <option value="200">200 ريال</option>
                                        <option value="100">100 ريال</option>
                                        <option value="50">50 ريال</option>
                                        <option value="20">20 ريال</option>
                                        <option value="10">10 ريال</option>
                                        <option value="5">5 ريال</option>
                                        <option value="1">1 ريال</option>
                                        <option value="0.5">0.5 ريال (نصف ريال)</option>
                                        <option value="0.25">0.25 ريال (ربع ريال)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editQuantity" class="form-label">عدد الأوراق</label>
                                    <input type="number" class="form-control" id="editQuantity" min="1" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editCashTotal" class="form-label">المجموع</label>
                                    <input type="number" class="form-control" id="editCashTotal" readonly>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-success" onclick="saveCashReceiptEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Postpaid Sale Modal -->
            <div class="modal fade" id="addEditPostpaidSaleModal" tabindex="-1" aria-labelledby="addEditPostpaidSaleModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title" id="addEditPostpaidSaleModalLabel">
                                <i class="icon">📄</i> <span id="postpaidSaleModalTitle">إضافة مبيعة آجلة</span>
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="postpaidSaleEditForm">
                                <div class="mb-3">
                                    <label for="postpaidSaleCustomerName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="postpaidSaleCustomerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="postpaidSaleAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="postpaidSaleAmount" step="0.01" min="0" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-warning" onclick="savePostpaidSaleEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Customer Receipt Modal -->
            <div class="modal fade" id="addEditCustomerReceiptModal" tabindex="-1" aria-labelledby="addEditCustomerReceiptModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="addEditCustomerReceiptModalLabel">
                                <i class="icon">👤</i> <span id="customerReceiptModalTitle">إضافة مقبوضة عميل</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="customerReceiptEditForm">
                                <div class="mb-3">
                                    <label for="customerReceiptEditCustomerName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="customerReceiptEditCustomerName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="customerReceiptEditAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="customerReceiptEditAmount" step="0.01" min="0" required>
                                </div>
                                <div class="mb-3">
                                    <label for="customerReceiptEditPaymentType" class="form-label">نوع الدفع</label>
                                    <select class="form-select" id="customerReceiptEditPaymentType" required>
                                        <option value="">اختر نوع الدفع</option>
                                        <option value="نقد">نقد</option>
                                        <option value="شيك">شيك</option>
                                        <option value="تحويل">تحويل</option>
                                        <option value="بطاقة">بطاقة</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-info" onclick="saveCustomerReceiptEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Return Invoice Modal -->
            <div class="modal fade" id="addEditReturnInvoiceModal" tabindex="-1" aria-labelledby="addEditReturnInvoiceModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="addEditReturnInvoiceModalLabel">
                                <i class="icon">↩️</i> <span id="returnInvoiceModalTitle">إضافة فاتورة مرتجع</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="returnInvoiceEditForm">
                                <div class="mb-3">
                                    <label for="returnInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" id="returnInvoiceNumber" required>
                                </div>
                                <div class="mb-3">
                                    <label for="returnInvoiceAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="returnInvoiceAmount" step="0.01" min="0" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-danger" onclick="saveReturnInvoiceEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add Supplier Modal -->
            <div class="modal fade" id="addEditSupplierModal" tabindex="-1" aria-labelledby="addEditSupplierModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-dark text-white">
                            <h5 class="modal-title" id="addEditSupplierModalLabel">
                                <i class="icon">🏭</i> <span id="supplierModalTitle">إضافة مورد</span>
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <form id="supplierEditForm">
                                <div class="mb-3">
                                    <label for="supplierEditName" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="supplierEditName" required>
                                </div>
                                <div class="mb-3">
                                    <label for="supplierEditAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="supplierEditAmount" step="0.01" min="0.01" required>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-dark" onclick="saveSupplierEdit()">
                                <i class="icon">💾</i> حفظ
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reconciliation Section -->
            <div id="reconciliation-section" class="content-section active">
                <div class="container-fluid">
                    <h2 class="section-title">التصفية الجديدة</h2>
                    
                    <!-- Start New Reconciliation -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>ابدأ تصفية جديدة</h5>
                        </div>
                        <div class="card-body">
                            <form id="newReconciliationForm" class="row g-3">
                                <div class="col-md-3">
                                    <label for="branchSelect" class="form-label">الفرع</label>
                                    <select class="form-select" id="branchSelect" required>
                                        <option value="">اختر الفرع</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="cashierSelect" class="form-label">اسم الكاشير</label>
                                    <select class="form-select" id="cashierSelect" required>
                                        <option value="">اختر الكاشير</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="cashierNumber" class="form-label">رقم الكاشير</label>
                                    <input type="text" class="form-control" id="cashierNumber" readonly>
                                </div>
                                <div class="col-md-2">
                                    <label for="accountantSelect" class="form-label">اسم المحاسب</label>
                                    <select class="form-select" id="accountantSelect" required>
                                        <option value="">اختر المحاسب</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="reconciliationDate" class="form-label">التاريخ</label>
                                    <input type="date" class="form-control" id="reconciliationDate" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">ابدأ التصفية</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Current Reconciliation Info -->
                    <div id="currentReconciliationInfo" class="alert alert-info" style="display: none;">
                        <strong>التصفية الحالية:</strong> <span id="currentReconciliationDetails"></span>
                    </div>

                    <!-- Bank Receipts Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>المقبوضات البنكية</h5>
                        </div>
                        <div class="card-body">
                            <form id="bankReceiptForm" class="row g-3">
                                <div class="col-md-3">
                                    <label for="operationType" class="form-label">نوع العملية</label>
                                    <select class="form-select" id="operationType" required>
                                        <option value="">اختر نوع العملية</option>
                                        <option value="مدى">مدى</option>
                                        <option value="فيزا">فيزا</option>
                                        <option value="ماستر كارد">ماستر كارد</option>
                                        <option value="أمريكان إكسبريس">أمريكان إكسبريس</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="atmSelect" class="form-label">الجهاز</label>
                                    <select class="form-select" id="atmSelect" required>
                                        <option value="">اختر الجهاز</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="bankName" class="form-label">اسم البنك</label>
                                    <input type="text" class="form-control" id="bankName" readonly>
                                </div>
                                <div class="col-md-3">
                                    <label for="bankAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="bankAmount" step="0.01" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>
                            
                            <!-- Bank Receipts Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>نوع العملية</th>
                                            <th>الجهاز</th>
                                            <th>البنك</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bankReceiptsTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th colspan="3">المجموع</th>
                                            <th id="bankReceiptsTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Cash Receipts Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>المقبوضات النقدية</h5>
                        </div>
                        <div class="card-body">
                            <form id="cashReceiptForm" class="row g-3">
                                <div class="col-md-6">
                                    <label for="denomination" class="form-label">فئة الورقة</label>
                                    <select class="form-select" id="denomination" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="500">500 ريال</option>
                                        <option value="200">200 ريال</option>
                                        <option value="100">100 ريال</option>
                                        <option value="50">50 ريال</option>
                                        <option value="20">20 ريال</option>
                                        <option value="10">10 ريال</option>
                                        <option value="5">5 ريال</option>
                                        <option value="1">1 ريال</option>
                                        <option value="0.5">0.5 ريال (نصف ريال)</option>
                                        <option value="0.25">0.25 ريال (ربع ريال)</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="quantity" class="form-label">عدد الأوراق</label>
                                    <input type="number" class="form-control" id="quantity" min="1" required>
                                </div>
                                <div class="col-md-3">
                                    <label for="cashTotal" class="form-label">المجموع</label>
                                    <input type="number" class="form-control" id="cashTotal" readonly>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>

                            <!-- Cash Receipts Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الفئة</th>
                                            <th>العدد</th>
                                            <th>المجموع</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cashReceiptsTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th colspan="2">المجموع الكلي</th>
                                            <th id="cashReceiptsTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Postpaid Sales Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>المبيعات الآجلة</h5>
                        </div>
                        <div class="card-body">
                            <form id="postpaidSaleForm" class="row g-3">
                                <div class="col-md-6">
                                    <label for="customerName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="customerName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="postpaidAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="postpaidAmount" step="0.01" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>

                            <!-- Postpaid Sales Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="postpaidSalesTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th>المجموع</th>
                                            <th id="postpaidSalesTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Receipts Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>مقبوضات العملاء</h5>
                        </div>
                        <div class="card-body">
                            <form id="customerReceiptForm" class="row g-3">
                                <div class="col-md-4">
                                    <label for="customerReceiptName" class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="customerReceiptName" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="customerReceiptAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="customerReceiptAmount" step="0.01" min="0.01" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="customerReceiptPaymentType" class="form-label">نوع الدفع</label>
                                    <select class="form-select" id="customerReceiptPaymentType" required>
                                        <option value="">اختر نوع الدفع</option>
                                        <option value="نقد">نقد</option>
                                        <option value="شيك">شيك</option>
                                        <option value="تحويل">تحويل</option>
                                        <option value="بطاقة">بطاقة</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>

                            <!-- Customer Receipts Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>المبلغ</th>
                                            <th>نوع الدفع</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="customerReceiptsTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th colspan="2">المجموع</th>
                                            <th id="customerReceiptsTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Return Invoices Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>فواتير المرتجعات</h5>
                        </div>
                        <div class="card-body">
                            <form id="returnInvoiceForm" class="row g-3">
                                <div class="col-md-6">
                                    <label for="invoiceNumber" class="form-label">رقم الفاتورة</label>
                                    <input type="text" class="form-control" id="invoiceNumber" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="returnAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="returnAmount" step="0.01" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>

                            <!-- Return Invoices Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="returnInvoicesTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-info">
                                            <th>المجموع</th>
                                            <th id="returnInvoicesTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Suppliers Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>الموردين <small class="text-muted">(للعرض فقط - لا يؤثر على المجاميع)</small></h5>
                        </div>
                        <div class="card-body">
                            <form id="supplierForm" class="row g-3">
                                <div class="col-md-6">
                                    <label for="supplierMainName" class="form-label">اسم المورد</label>
                                    <input type="text" class="form-control" id="supplierMainName" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="supplierMainAmount" class="form-label">المبلغ</label>
                                    <input type="number" class="form-control" id="supplierMainAmount" step="0.01" min="0.01" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">إضافة</button>
                                </div>
                            </form>

                            <!-- Suppliers Table -->
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>اسم المورد</th>
                                            <th>المبلغ</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="suppliersTable">
                                    </tbody>
                                    <tfoot>
                                        <tr class="table-warning">
                                            <th>المجموع (للعرض فقط)</th>
                                            <th id="suppliersTotal">0.00</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Reconciliation Summary -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>ملخص التصفية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي المقبوضات البنكية:</label>
                                        <div class="summary-value text-currency" id="summaryBankTotal">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي المقبوضات النقدية:</label>
                                        <div class="summary-value text-currency" id="summaryCashTotal">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي المبيعات الآجلة:</label>
                                        <div class="summary-value text-currency" id="summaryPostpaidTotal">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي مقبوضات العملاء:</label>
                                        <div class="summary-value text-currency" id="summaryCustomerTotal">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي المرتجعات:</label>
                                        <div class="summary-value text-currency" id="summaryReturnTotal">0.00</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="summary-item">
                                        <label for="systemSales" class="form-label">مبيعات النظام:</label>
                                        <input type="number" class="form-control" id="systemSales" step="0.01" placeholder="أدخل مبيعات النظام">
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">إجمالي المقبوضات:</label>
                                        <div class="summary-value text-currency" id="totalReceipts">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <label class="form-label">الفائض أو العجز:</label>
                                        <div class="summary-value" id="surplusDeficit">0.00</div>
                                    </div>
                                    <div class="summary-item">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-print btn-lg" id="printNewReconciliationBtn" title="طباعة مع خيارات الأقسام">
                                                <i class="icon">🖨️</i> طباعة متقدمة
                                            </button>
                                            <button type="button" class="btn btn-outline-print btn-lg" id="quickPrintBtn" title="طباعة سريعة لجميع الأقسام">
                                                <i class="icon">⚡</i> طباعة سريعة
                                            </button>
                                            <button type="button" class="btn btn-outline-print btn-lg" id="savePdfBtn">
                                                <i class="icon">📄</i> حفظ PDF
                                            </button>
                                        </div>
                                        <button type="button" class="btn btn-success btn-lg ms-2" id="saveReconciliationBtn">
                                            <i class="icon">💾</i> حفظ التصفية
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branches Management Section -->
            <div id="branches-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">إدارة الفروع</h2>

                    <!-- Add New Branch -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">إضافة فرع جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="branchForm">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label for="branchName" class="form-label">اسم الفرع</label>
                                        <input type="text" class="form-control" id="branchName" required>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="branchAddress" class="form-label">عنوان الفرع</label>
                                        <input type="text" class="form-control" id="branchAddress">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="branchPhone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="branchPhone">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="branchStatus" class="form-label">حالة الفرع</label>
                                        <select class="form-select" id="branchStatus">
                                            <option value="1">نشط</option>
                                            <option value="0">غير نشط</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary">إضافة الفرع</button>
                                    <button type="button" class="btn btn-secondary" onclick="clearBranchForm()">مسح</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Branches List -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">قائمة الفروع</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="loadBranches()">
                                <i class="icon">🔄</i> تحديث
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المعرف</th>
                                            <th>اسم الفرع</th>
                                            <th>العنوان</th>
                                            <th>رقم الهاتف</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="branchesTable">
                                        <tr>
                                            <td colspan="7" class="text-center">جاري تحميل البيانات...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cashiers Management Section -->
            <div id="cashiers-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">إدارة الكاشير</h2>

                    <!-- Add New Cashier -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>إضافة كاشير جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="addCashierForm" class="row g-3">
                                <div class="col-md-4">
                                    <label for="cashierNameInput" class="form-label">اسم الكاشير</label>
                                    <input type="text" class="form-control" id="cashierNameInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="cashierNumberInput" class="form-label">رقم الكاشير</label>
                                    <input type="text" class="form-control" id="cashierNumberInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="cashierBranchSelect" class="form-label">الفرع</label>
                                    <select class="form-select" id="cashierBranchSelect" required>
                                        <option value="">اختر الفرع</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">إضافة الكاشير</button>
                                    <button type="button" class="btn btn-secondary ms-2" id="cancelCashierEdit">إلغاء</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Cashiers List -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة الكاشير</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم الكاشير</th>
                                            <th>رقم الكاشير</th>
                                            <th>الفرع</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cashiersListTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Admins Management Section -->
            <div id="admins-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">إدارة المسؤولين</h2>

                    <!-- Add New Admin -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>إضافة مسؤول جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="addAdminForm" class="row g-3">
                                <div class="col-md-4">
                                    <label for="adminNameInput" class="form-label">اسم المسؤول</label>
                                    <input type="text" class="form-control" id="adminNameInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="adminUsernameInput" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="adminUsernameInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="adminPasswordInput" class="form-label">كلمة المرور</label>
                                    <input type="password" class="form-control" id="adminPasswordInput" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">إضافة المسؤول</button>
                                    <button type="button" class="btn btn-secondary ms-2" id="cancelAdminEdit">إلغاء</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Admins List -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة المسؤولين</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم المسؤول</th>
                                            <th>اسم المستخدم</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="adminsListTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Accountants Management Section -->
            <div id="accountants-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">إدارة المحاسبين</h2>

                    <!-- Add New Accountant -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>إضافة محاسب جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="addAccountantForm" class="row g-3">
                                <div class="col-md-12">
                                    <label for="accountantNameInput" class="form-label">اسم المحاسب</label>
                                    <input type="text" class="form-control" id="accountantNameInput" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">إضافة المحاسب</button>
                                    <button type="button" class="btn btn-secondary ms-2" id="cancelAccountantEdit">إلغاء</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Accountants List -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة المحاسبين</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم المحاسب</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="accountantsListTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ATMs Management Section -->
            <div id="atms-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">إدارة أجهزة الصراف</h2>

                    <!-- Add New ATM -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>إضافة جهاز صراف جديد</h5>
                        </div>
                        <div class="card-body">
                            <form id="addAtmForm" class="row g-3">
                                <div class="col-md-4">
                                    <label for="atmNameInput" class="form-label">اسم الجهاز</label>
                                    <input type="text" class="form-control" id="atmNameInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="atmBankInput" class="form-label">اسم البنك</label>
                                    <input type="text" class="form-control" id="atmBankInput" required>
                                </div>
                                <div class="col-md-4">
                                    <label for="atmLocationInput" class="form-label">الموقع</label>
                                    <input type="text" class="form-control" id="atmLocationInput" placeholder="مثال: الفرع الرئيسي">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">إضافة الجهاز</button>
                                    <button type="button" class="btn btn-secondary ms-2" id="cancelAtmEdit">إلغاء</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- ATMs List -->
                    <div class="card">
                        <div class="card-header">
                            <h5>قائمة أجهزة الصراف</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الرقم</th>
                                            <th>اسم الجهاز</th>
                                            <th>اسم البنك</th>
                                            <th>الموقع</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="atmsListTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Saved Reconciliations Section -->
            <div id="saved-reconciliations-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">التصفيات المحفوظة</h2>

                    <!-- Search and Filter -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>البحث والتصفية</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="searchCashierFilter" class="form-label">الكاشير</label>
                                    <select class="form-select" id="searchCashierFilter">
                                        <option value="">جميع الكاشير</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchDateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="searchDateTo">
                                </div>
                                <div class="col-md-3">
                                    <label for="searchStatus" class="form-label">الحالة</label>
                                    <select class="form-select" id="searchStatus">
                                        <option value="">جميع الحالات</option>
                                        <option value="draft">مسودة</option>
                                        <option value="completed">مكتملة</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary" id="searchReconciliationsBtn">
                                        <i class="icon">🔍</i> بحث
                                    </button>
                                    <button type="button" class="btn btn-secondary ms-2" id="clearSearchBtn">
                                        <i class="icon">🗑️</i> مسح البحث
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reconciliations List -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">قائمة التصفيات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم التصفية</th>
                                            <th>الكاشير</th>
                                            <th>المحاسب</th>
                                            <th>التاريخ</th>
                                            <th>إجمالي المقبوضات</th>
                                            <th>مبيعات النظام</th>
                                            <th>الفائض/العجز</th>
                                            <th>الحالة</th>
                                            <th>آخر تعديل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="savedReconciliationsTable">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports Section -->
            <div id="reports-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">تقارير التصفيات</h2>

                    <!-- Enhanced Report Filters -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>🔍 تصفية التقارير المتقدمة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- Date Range -->
                                <div class="col-md-3">
                                    <label for="reportDateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="reportDateFrom">
                                </div>
                                <div class="col-md-3">
                                    <label for="reportDateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="reportDateTo">
                                </div>

                                <!-- Branch Filter -->
                                <div class="col-md-2">
                                    <label for="reportBranchFilter" class="form-label">الفرع</label>
                                    <select class="form-select" id="reportBranchFilter">
                                        <option value="">جميع الفروع</option>
                                    </select>
                                </div>

                                <!-- Cashier Filter -->
                                <div class="col-md-2">
                                    <label for="reportCashierFilter" class="form-label">الكاشير</label>
                                    <select class="form-select" id="reportCashierFilter">
                                        <option value="">جميع الكاشير</option>
                                    </select>
                                </div>

                                <!-- Accountant Filter -->
                                <div class="col-md-2">
                                    <label for="reportAccountantFilter" class="form-label">المحاسب</label>
                                    <select class="form-select" id="reportAccountantFilter">
                                        <option value="">جميع المحاسبين</option>
                                    </select>
                                </div>

                                <!-- Status Filter -->
                                <div class="col-md-3">
                                    <label for="reportStatusFilter" class="form-label">الحالة</label>
                                    <select class="form-select" id="reportStatusFilter">
                                        <option value="">جميع الحالات</option>
                                        <option value="completed">مكتملة</option>
                                        <option value="draft">مسودة</option>
                                    </select>
                                </div>

                                <!-- Amount Range -->
                                <div class="col-md-3">
                                    <label for="reportMinAmount" class="form-label">أقل مبلغ</label>
                                    <input type="number" class="form-control" id="reportMinAmount" step="0.01" placeholder="0.00">
                                </div>
                                <div class="col-md-3">
                                    <label for="reportMaxAmount" class="form-label">أعلى مبلغ</label>
                                    <input type="number" class="form-control" id="reportMaxAmount" step="0.01" placeholder="999999.99">
                                </div>

                                <!-- Search Text -->
                                <div class="col-md-3">
                                    <label for="reportSearchText" class="form-label">البحث النصي</label>
                                    <input type="text" class="form-control" id="reportSearchText" placeholder="ابحث في التصفيات...">
                                </div>

                                <!-- Action Buttons -->
                                <div class="col-12">
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-success" id="generateReportBtn">
                                            <i class="icon">📊</i> إنشاء التقرير
                                        </button>
                                        <button type="button" class="btn btn-info" id="exportReportPdfBtn">
                                            <i class="icon">📄</i> تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-success" id="exportReportExcelBtn">
                                            <i class="icon">📊</i> تصدير Excel
                                        </button>
                                        <button type="button" class="btn btn-primary" id="printReportBtn">
                                            <i class="icon">🖨️</i> طباعة
                                        </button>
                                    </div>
                                    <button type="button" class="btn btn-outline-secondary ms-2" id="clearReportFiltersBtn">
                                        <i class="icon">🔄</i> مسح التصفية
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Report Results -->
                    <div class="card" id="reportResultsCard" style="display: none;">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">📊 نتائج التقرير</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" id="toggleSummaryViewBtn">
                                    <i class="icon">📈</i> عرض الإحصائيات
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-info" id="toggleChartViewBtn">
                                    <i class="icon">📊</i> عرض الرسوم البيانية
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Summary Statistics -->
                            <div id="reportSummary" class="row mb-4">
                                <!-- Summary cards will be populated here -->
                            </div>

                            <!-- Charts Section -->
                            <div id="reportChartsSection" class="mb-4" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">📊 توزيع التصفيات حسب الكاشير</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="cashierDistributionChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">📈 اتجاه المبيعات</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="salesTrendChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Table -->
                            <div class="table-responsive">
                                <table class="table table-striped table-hover" id="reportResultsTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>رقم التصفية</th>
                                            <th>التاريخ</th>
                                            <th>الكاشير</th>
                                            <th>المحاسب</th>
                                            <th>إجمالي المقبوضات</th>
                                            <th>مبيعات النظام</th>
                                            <th>الفائض/العجز</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="reportResultsTableBody">
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <span id="reportPaginationInfo">عرض 0 من 0 نتيجة</span>
                                </div>
                                <nav aria-label="تنقل التقرير">
                                    <ul class="pagination pagination-sm mb-0" id="reportPagination">
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>



            <!-- Cashier Performance Comparison Section -->
            <div id="cashier-performance-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">🏆 مقارنة أداء الكاشير</h2>

                    <!-- Filters Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>🔍 فلاتر المقارنة</h5>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="performanceDateFrom" class="form-label">من تاريخ</label>
                                    <input type="date" class="form-control" id="performanceDateFrom">
                                </div>
                                <div class="col-md-4">
                                    <label for="performanceDateTo" class="form-label">إلى تاريخ</label>
                                    <input type="date" class="form-control" id="performanceDateTo">
                                </div>
                                <div class="col-md-4">
                                    <label for="performanceBranch" class="form-label">الفرع</label>
                                    <select class="form-select" id="performanceBranch">
                                        <option value="">جميع الفروع</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <button type="button" class="btn btn-primary btn-lg" id="generatePerformanceBtn">
                                        <i class="icon">🚀</i> بدء المقارنة
                                    </button>
                                    <button type="button" class="btn btn-success ms-2" id="exportPerformancePdfBtn" style="display: none;">
                                        <i class="icon">📄</i> تصدير PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Loading Spinner -->
                    <div id="performanceLoading" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3">جاري تحليل أداء الكاشيرين...</p>
                    </div>

                    <!-- Results Section -->
                    <div id="performanceResults" style="display: none;">
                        <!-- Summary Cards -->
                        <div class="row mb-4" id="performanceSummary">
                            <!-- Summary cards will be inserted here -->
                        </div>

                        <!-- Cashier Ranking Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">🏆 ترتيب الكاشيرين</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="cashierRankingList" class="row">
                                            <!-- Cashier ranking cards will be inserted here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Detailed Cashier Cards -->
                        <div class="row" id="cashierPerformanceCards">
                            <!-- Individual cashier performance cards will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Reports Section -->
            <div id="advanced-reports-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">التقارير المتقدمة</h2>

                    <!-- Report Types -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>تقرير المقبوضات عبر الزمن</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label for="timeReportType" class="form-label">نوع التقرير</label>
                                            <select class="form-select" id="timeReportType">
                                                <option value="daily">يومي</option>
                                                <option value="weekly">أسبوعي</option>
                                                <option value="monthly">شهري</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label for="timeReportFrom" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="timeReportFrom">
                                        </div>
                                        <div class="col-6">
                                            <label for="timeReportTo" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="timeReportTo">
                                        </div>
                                        <div class="col-12">
                                            <button type="button" class="btn btn-primary" id="generateTimeReportBtn">
                                                <i class="icon">📈</i> إنشاء التقرير
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h5>تقرير أجهزة الصراف</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-12">
                                            <label for="atmReportFilter" class="form-label">الجهاز</label>
                                            <select class="form-select" id="atmReportFilter">
                                                <option value="">جميع الأجهزة</option>
                                            </select>
                                        </div>
                                        <div class="col-6">
                                            <label for="atmReportFrom" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="atmReportFrom">
                                        </div>
                                        <div class="col-6">
                                            <label for="atmReportTo" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="atmReportTo">
                                        </div>
                                        <div class="col-12">
                                            <div class="d-grid gap-2">
                                                <button type="button" class="btn btn-primary" id="generateAtmReportBtn">
                                                    <i class="icon">🏧</i> تقرير مجمع
                                                </button>
                                                <button type="button" class="btn btn-info" id="generateDetailedAtmReportBtn">
                                                    <i class="icon">📊</i> تقرير تحليلي مفصل
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Report Results -->
                    <div class="card" id="advancedReportResultsCard" style="display: none;">
                        <div class="card-header">
                            <h5>نتائج التقرير المتقدم</h5>
                        </div>
                        <div class="card-body">
                            <div id="advancedReportContent">
                                <!-- Content will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Results Section for Advanced Reports -->
                    <div class="row" id="advancedReportsResults" style="display: none;">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0" id="advancedReportTitle">نتائج التقرير</h5>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-success btn-sm" id="exportAdvancedReportPdf">
                                            <i class="icon">📄</i> تصدير PDF
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" id="exportAdvancedReportExcel">
                                            <i class="icon">📊</i> تصدير Excel
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" id="printAdvancedReport">
                                            <i class="icon">🖨️</i> طباعة
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Summary Statistics -->
                                    <div class="row mb-4" id="advancedReportSummary">
                                        <!-- Summary cards will be populated here -->
                                    </div>



                                    <!-- Data Table -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead id="advancedReportTableHead">
                                                <!-- Table headers will be populated dynamically -->
                                            </thead>
                                            <tbody id="advancedReportTableBody">
                                                <!-- Table data will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    <nav aria-label="صفحات التقرير المتقدم" id="advancedReportPaginationNav" style="display: none;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <small class="text-muted" id="advancedReportPaginationInfo"></small>
                                            </div>
                                            <ul class="pagination pagination-sm mb-0" id="advancedReportPagination">
                                                <!-- Pagination will be populated here -->
                                            </ul>
                                        </div>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed ATM Report Modal -->
            <div class="modal fade" id="detailedAtmReportModal" tabindex="-1" aria-labelledby="detailedAtmReportModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="detailedAtmReportModalLabel">التقرير التحليلي المفصل لأجهزة الصراف</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Advanced Filters -->
                            <div class="card mb-4">
                                <div class="card-header">
                                    <h6 class="mb-0">خيارات التصفية المتقدمة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <label for="detailedAtmFilter" class="form-label">الجهاز</label>
                                            <select class="form-select" id="detailedAtmFilter">
                                                <option value="">جميع الأجهزة</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedOperationTypeFilter" class="form-label">نوع العملية</label>
                                            <select class="form-select" id="detailedOperationTypeFilter">
                                                <option value="">جميع العمليات</option>
                                                <option value="مدى">مدى</option>
                                                <option value="فيزا">فيزا</option>
                                                <option value="ماستر كارد">ماستر كارد</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedCashierFilter" class="form-label">الكاشير</label>
                                            <select class="form-select" id="detailedCashierFilter">
                                                <option value="">جميع الكاشيرين</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedMinAmount" class="form-label">الحد الأدنى للمبلغ</label>
                                            <input type="number" class="form-control" id="detailedMinAmount" placeholder="0" step="0.01">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedDateFrom" class="form-label">من تاريخ</label>
                                            <input type="date" class="form-control" id="detailedDateFrom">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedDateTo" class="form-label">إلى تاريخ</label>
                                            <input type="date" class="form-control" id="detailedDateTo">
                                        </div>
                                        <div class="col-md-3">
                                            <label for="detailedMaxAmount" class="form-label">الحد الأعلى للمبلغ</label>
                                            <input type="number" class="form-control" id="detailedMaxAmount" placeholder="بدون حد" step="0.01">
                                        </div>
                                        <div class="col-md-3">
                                            <button type="button" class="btn btn-primary mt-4" id="applyDetailedFiltersBtn">
                                                <i class="icon">🔍</i> تطبيق التصفية
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Results Section -->
                            <div class="card" id="detailedAtmReportResults" style="display: none;">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0" id="detailedReportTitle">نتائج التقرير التحليلي</h6>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-success btn-sm" id="exportDetailedAtmReportExcel">
                                            <i class="icon">📊</i> تصدير Excel
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" id="printDetailedAtmReport">
                                            <i class="icon">🖨️</i> طباعة
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Summary Statistics -->
                                    <div class="row mb-3" id="detailedReportSummary">
                                        <!-- Summary cards will be populated here -->
                                    </div>

                                    <!-- Search and Sort -->
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="detailedReportSearch" placeholder="البحث في النتائج...">
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="detailedReportSort">
                                                <option value="date_desc">التاريخ (الأحدث أولاً)</option>
                                                <option value="date_asc">التاريخ (الأقدم أولاً)</option>
                                                <option value="amount_desc">المبلغ (الأعلى أولاً)</option>
                                                <option value="amount_asc">المبلغ (الأقل أولاً)</option>
                                                <option value="atm_name">اسم الجهاز</option>
                                                <option value="operation_type">نوع العملية</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" id="detailedReportPageSize">
                                                <option value="25">25 عنصر</option>
                                                <option value="50" selected>50 عنصر</option>
                                                <option value="100">100 عنصر</option>
                                                <option value="all">جميع العناصر</option>
                                            </select>
                                        </div>
                                    </div>

                                    <!-- Data Table -->
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover table-sm">
                                            <thead class="table-dark">
                                                <tr>
                                                    <th>التاريخ والوقت</th>
                                                    <th>نوع العملية</th>
                                                    <th>الجهاز</th>
                                                    <th>الموقع</th>
                                                    <th>البنك</th>
                                                    <th>المبلغ</th>
                                                    <th>الكاشير</th>
                                                    <th>رقم التصفية</th>
                                                </tr>
                                            </thead>
                                            <tbody id="detailedAtmReportTableBody">
                                                <!-- Table data will be populated here -->
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Pagination -->
                                    <nav aria-label="صفحات التقرير المفصل" id="detailedReportPaginationNav" style="display: none;">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <small class="text-muted" id="detailedReportPaginationInfo"></small>
                                            </div>
                                            <ul class="pagination pagination-sm mb-0" id="detailedReportPagination">
                                                <!-- Pagination will be populated here -->
                                            </ul>
                                        </div>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="content-section">
                <div class="container-fluid">
                    <h2 class="section-title">الإعدادات</h2>

                    <!-- Settings Navigation Tabs -->
                    <div class="card settings-tabs-container">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general-settings" type="button" role="tab">
                                        <i class="icon">🏢</i> الإعدادات العامة
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="print-tab" data-bs-toggle="tab" data-bs-target="#print-settings" type="button" role="tab">
                                        <i class="icon">🖨️</i> إعدادات الطباعة
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database-settings" type="button" role="tab">
                                        <i class="icon">💾</i> قاعدة البيانات
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-settings" type="button" role="tab">
                                        <i class="icon">👥</i> المستخدمين
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports-settings" type="button" role="tab">
                                        <i class="icon">📊</i> التقارير
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system-info" type="button" role="tab">
                                        <i class="icon">ℹ️</i> معلومات النظام
                                    </button>
                                </li>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link" id="updates-tab" data-bs-toggle="tab" data-bs-target="#updates-settings" type="button" role="tab">
                                        <i class="icon">🔄</i> التحديثات
                                    </button>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="settingsTabContent">
                                <!-- General Settings Tab -->
                                <div class="tab-pane fade show active" id="general-settings" role="tabpanel">
                                    <h5 class="mb-4">الإعدادات العامة للنظام</h5>

                                    <form id="generalSettingsForm">
                                        <div class="row g-4">
                                            <!-- Company Information -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">معلومات الشركة</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="companyName" class="form-label">اسم الشركة</label>
                                                <input type="text" class="form-control" id="companyName" name="companyName" placeholder="اسم الشركة">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="companyPhone" class="form-label">رقم الهاتف</label>
                                                <input type="tel" class="form-control" id="companyPhone" name="companyPhone" placeholder="رقم الهاتف">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="companyEmail" class="form-label">البريد الإلكتروني</label>
                                                <input type="email" class="form-control" id="companyEmail" name="companyEmail" placeholder="البريد الإلكتروني">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="companyWebsite" class="form-label">الموقع الإلكتروني</label>
                                                <input type="url" class="form-control" id="companyWebsite" name="companyWebsite" placeholder="الموقع الإلكتروني">
                                            </div>
                                            <div class="col-12">
                                                <label for="companyAddress" class="form-label">العنوان</label>
                                                <textarea class="form-control" id="companyAddress" name="companyAddress" rows="3" placeholder="عنوان الشركة"></textarea>
                                            </div>

                                            <!-- Logo Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">شعار الشركة</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="companyLogo" class="form-label">رفع الشعار</label>
                                                <input type="file" class="form-control" id="companyLogo" accept="image/*">
                                                <small class="text-muted">الأنواع المدعومة: PNG, JPG, SVG (الحد الأقصى: 2MB)</small>
                                            </div>
                                            <div class="col-md-6">
                                                <label class="form-label">معاينة الشعار</label>
                                                <div class="border rounded p-3 text-center" id="logoPreview" style="min-height: 100px;">
                                                    <span class="text-muted">لا يوجد شعار</span>
                                                </div>
                                            </div>

                                            <!-- System Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">إعدادات النظام</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="systemLanguage" class="form-label">لغة النظام</label>
                                                <select class="form-select" id="systemLanguage" name="systemLanguage">
                                                    <option value="ar" selected>العربية</option>
                                                    <option value="en">English</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="systemTheme" class="form-label">مظهر النظام</label>
                                                <select class="form-select" id="systemTheme" name="systemTheme">
                                                    <option value="light" selected>فاتح</option>
                                                    <option value="dark">داكن</option>
                                                    <option value="auto">تلقائي</option>
                                                </select>
                                            </div>

                                            <!-- Save Button -->
                                            <div class="col-12">
                                                <hr>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="icon">💾</i> حفظ الإعدادات العامة
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" id="resetGeneralSettings">
                                                    <i class="icon">🔄</i> إعادة تعيين
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- Print Settings Tab -->
                                <div class="tab-pane fade" id="print-settings" role="tabpanel">
                                    <h5 class="mb-4">إعدادات الطباعة</h5>

                                    <form id="printSettingsForm">
                                        <div class="row g-4">
                                            <!-- Paper Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">إعدادات الورق</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="paperSize" class="form-label">حجم الورق</label>
                                                <select class="form-select" id="paperSize">
                                                    <option value="A4" selected>A4 (210 × 297 مم)</option>
                                                    <option value="A3">A3 (297 × 420 مم)</option>
                                                    <option value="Letter">Letter (216 × 279 مم)</option>
                                                    <option value="Legal">Legal (216 × 356 مم)</option>
                                                    <option value="A5">A5 (148 × 210 مم)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="paperOrientation" class="form-label">اتجاه الورق</label>
                                                <select class="form-select" id="paperOrientation">
                                                    <option value="portrait" selected>عمودي (Portrait)</option>
                                                    <option value="landscape">أفقي (Landscape)</option>
                                                </select>
                                            </div>

                                            <!-- Font Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">إعدادات الخط</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="fontFamily" class="form-label">نوع الخط</label>
                                                <select class="form-select" id="fontFamily">
                                                    <option value="Cairo" selected>Cairo</option>
                                                    <option value="Amiri">Amiri</option>
                                                    <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                                    <option value="Arial">Arial</option>
                                                    <option value="Times New Roman">Times New Roman</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="fontSize" class="form-label">حجم الخط</label>
                                                <select class="form-select" id="fontSize">
                                                    <option value="small">صغير (10pt)</option>
                                                    <option value="normal" selected>عادي (12pt)</option>
                                                    <option value="large">كبير (14pt)</option>
                                                    <option value="extra-large">كبير جداً (16pt)</option>
                                                </select>
                                            </div>

                                            <!-- Margin Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">إعدادات الهوامش</h6>
                                            </div>
                                            <div class="col-md-3">
                                                <label for="marginTop" class="form-label">الهامش العلوي (مم)</label>
                                                <input type="number" class="form-control" id="marginTop" value="20" min="0" max="50">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="marginBottom" class="form-label">الهامش السفلي (مم)</label>
                                                <input type="number" class="form-control" id="marginBottom" value="20" min="0" max="50">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="marginLeft" class="form-label">الهامش الأيسر (مم)</label>
                                                <input type="number" class="form-control" id="marginLeft" value="15" min="0" max="50">
                                            </div>
                                            <div class="col-md-3">
                                                <label for="marginRight" class="form-label">الهامش الأيمن (مم)</label>
                                                <input type="number" class="form-control" id="marginRight" value="15" min="0" max="50">
                                            </div>

                                            <!-- Print Options -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">خيارات الطباعة</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printHeader" checked>
                                                    <label class="form-check-label" for="printHeader">
                                                        طباعة رأس الصفحة
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printFooter" checked>
                                                    <label class="form-check-label" for="printFooter">
                                                        طباعة تذييل الصفحة
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printLogo" checked>
                                                    <label class="form-check-label" for="printLogo">
                                                        طباعة شعار الشركة
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printPageNumbers" checked>
                                                    <label class="form-check-label" for="printPageNumbers">
                                                        طباعة أرقام الصفحات
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printDate" checked>
                                                    <label class="form-check-label" for="printDate">
                                                        طباعة التاريخ والوقت
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="printBorders">
                                                    <label class="form-check-label" for="printBorders">
                                                        طباعة حدود الجداول
                                                    </label>
                                                </div>
                                            </div>

                                            <!-- Save Button -->
                                            <div class="col-12">
                                                <hr>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="icon">💾</i> حفظ إعدادات الطباعة
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" id="resetPrintSettings">
                                                    <i class="icon">🔄</i> إعادة تعيين
                                                </button>
                                                <button type="button" class="btn btn-info ms-2" id="testPrintSettings">
                                                    <i class="icon">🖨️</i> اختبار الطباعة
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- Database Settings Tab -->
                                <div class="tab-pane fade" id="database-settings" role="tabpanel">
                                    <h5 class="mb-4">إعدادات قاعدة البيانات</h5>

                                    <div class="row g-4">
                                        <!-- Database Information -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">معلومات قاعدة البيانات</h6>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">حالة قاعدة البيانات</h6>
                                                    <p class="card-text">
                                                        <span class="badge bg-success">متصلة</span>
                                                        <small class="text-muted d-block mt-1">آخر تحديث: <span id="lastDbUpdate">جاري التحميل...</span></small>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">حجم قاعدة البيانات</h6>
                                                    <p class="card-text">
                                                        <span id="dbSize">جاري الحساب...</span>
                                                        <small class="text-muted d-block mt-1">عدد السجلات: <span id="recordCount">جاري العد...</span></small>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Backup Settings -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">النسخ الاحتياطي</h6>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="autoBackup" class="form-label">النسخ الاحتياطي التلقائي</label>
                                            <select class="form-select" id="autoBackup">
                                                <option value="disabled">معطل</option>
                                                <option value="daily" selected>يومياً</option>
                                                <option value="weekly">أسبوعياً</option>
                                                <option value="monthly">شهرياً</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="backupLocation" class="form-label">مجلد النسخ الاحتياطي</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="backupLocation" readonly placeholder="اختر مجلد الحفظ">
                                                <button class="btn btn-outline-secondary" type="button" id="selectBackupLocation">
                                                    <i class="icon">📁</i> تصفح
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Backup Actions -->
                                        <div class="col-12">
                                            <div class="d-flex gap-2 flex-wrap">
                                                <button type="button" class="btn btn-success" id="createBackupBtn">
                                                    <i class="icon">💾</i> إنشاء نسخة احتياطية الآن
                                                </button>
                                                <button type="button" class="btn btn-warning" id="restoreBackupBtn">
                                                    <i class="icon">📥</i> استعادة من نسخة احتياطية
                                                </button>
                                                <button type="button" class="btn btn-info" id="exportDataBtn">
                                                    <i class="icon">📊</i> تصدير البيانات
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Database Maintenance -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">صيانة قاعدة البيانات</h6>
                                        </div>
                                        <div class="col-12">
                                            <div class="alert alert-warning">
                                                <h6 class="alert-heading">تحذير!</h6>
                                                <p class="mb-2">عمليات الصيانة التالية قد تؤثر على أداء النظام. يُنصح بإنشاء نسخة احتياطية قبل تنفيذها.</p>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex gap-2 flex-wrap">
                                                <button type="button" class="btn btn-outline-primary" id="optimizeDbBtn">
                                                    <i class="icon">⚡</i> تحسين قاعدة البيانات
                                                </button>
                                                <button type="button" class="btn btn-outline-secondary" id="repairDbBtn">
                                                    <i class="icon">🔧</i> إصلاح قاعدة البيانات
                                                </button>
                                                <button type="button" class="btn btn-outline-info" id="analyzeDbBtn">
                                                    <i class="icon">📈</i> تحليل قاعدة البيانات
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Save Button -->
                                        <div class="col-12">
                                            <hr>
                                            <button type="button" class="btn btn-primary" id="saveDatabaseSettings">
                                                <i class="icon">💾</i> حفظ إعدادات قاعدة البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Users Settings Tab -->
                                <div class="tab-pane fade" id="users-settings" role="tabpanel">
                                    <h5 class="mb-4">إعدادات المستخدمين والصلاحيات</h5>

                                    <div class="row g-4">
                                        <!-- Current User Info -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">المستخدم الحالي</h6>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-primary text-white">
                                                <div class="card-body">
                                                    <h6 class="card-title">معلومات المستخدم</h6>
                                                    <p class="card-text">
                                                        <strong>اسم المستخدم:</strong> <span id="currentUsername">admin</span><br>
                                                        <strong>الصلاحية:</strong> <span id="currentUserRole">مدير النظام</span><br>
                                                        <strong>آخر دخول:</strong> <span id="lastLogin">الآن</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="card-title">إحصائيات الجلسة</h6>
                                                    <p class="card-text">
                                                        <strong>مدة الجلسة:</strong> <span id="sessionDuration">جاري الحساب...</span><br>
                                                        <strong>العمليات المنجزة:</strong> <span id="sessionOperations">0</span><br>
                                                        <strong>آخر نشاط:</strong> <span id="lastActivity">الآن</span>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Security Settings -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">إعدادات الأمان</h6>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="sessionTimeout" class="form-label">انتهاء صلاحية الجلسة (دقيقة)</label>
                                            <select class="form-select" id="sessionTimeout">
                                                <option value="30">30 دقيقة</option>
                                                <option value="60" selected>60 دقيقة</option>
                                                <option value="120">120 دقيقة</option>
                                                <option value="0">بدون انتهاء</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="autoLock" class="form-label">القفل التلقائي عند عدم النشاط</label>
                                            <select class="form-select" id="autoLock">
                                                <option value="disabled">معطل</option>
                                                <option value="5">5 دقائق</option>
                                                <option value="10" selected>10 دقائق</option>
                                                <option value="15">15 دقيقة</option>
                                            </select>
                                        </div>

                                        <!-- Password Change -->
                                        <div class="col-12">
                                            <h6 class="border-bottom pb-2 mb-3">تغيير كلمة المرور</h6>
                                        </div>
                                        <div class="col-md-4">
                                            <label for="currentPassword" class="form-label">كلمة المرور الحالية</label>
                                            <input type="password" class="form-control" id="currentPassword">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="newPassword" class="form-label">كلمة المرور الجديدة</label>
                                            <input type="password" class="form-control" id="newPassword">
                                        </div>
                                        <div class="col-md-4">
                                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                                            <input type="password" class="form-control" id="confirmPassword">
                                        </div>
                                        <div class="col-12">
                                            <button type="button" class="btn btn-warning" id="changePasswordBtn">
                                                <i class="icon">🔐</i> تغيير كلمة المرور
                                            </button>
                                        </div>

                                        <!-- Save Button -->
                                        <div class="col-12">
                                            <hr>
                                            <button type="button" class="btn btn-primary" id="saveUserSettings">
                                                <i class="icon">💾</i> حفظ إعدادات المستخدمين
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Reports Settings Tab -->
                                <div class="tab-pane fade" id="reports-settings" role="tabpanel">
                                    <h5 class="mb-4">إعدادات التقارير</h5>

                                    <form id="reportsSettingsForm">
                                        <div class="row g-4">
                                            <!-- Default Report Settings -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">الإعدادات الافتراضية للتقارير</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="defaultReportFormat" class="form-label">تنسيق التقرير الافتراضي</label>
                                                <select class="form-select" id="defaultReportFormat">
                                                    <option value="pdf" selected>PDF</option>
                                                    <option value="excel">Excel</option>
                                                    <option value="html">HTML</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="defaultDateRange" class="form-label">النطاق الزمني الافتراضي</label>
                                                <select class="form-select" id="defaultDateRange">
                                                    <option value="today">اليوم</option>
                                                    <option value="week" selected>آخر أسبوع</option>
                                                    <option value="month">آخر شهر</option>
                                                    <option value="quarter">آخر ربع سنة</option>
                                                    <option value="year">آخر سنة</option>
                                                </select>
                                            </div>

                                            <!-- Export Paths -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">مسارات الحفظ</h6>
                                            </div>
                                            <div class="col-12">
                                                <label for="reportsPath" class="form-label">مجلد حفظ التقارير</label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" id="reportsPath" readonly placeholder="اختر مجلد حفظ التقارير">
                                                    <button class="btn btn-outline-secondary" type="button" id="selectReportsPath">
                                                        <i class="icon">📁</i> تصفح
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Report Options -->
                                            <div class="col-12">
                                                <h6 class="border-bottom pb-2 mb-3">خيارات التقارير</h6>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                                    <label class="form-check-label" for="includeCharts">
                                                        تضمين الرسوم البيانية
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeSummary" checked>
                                                    <label class="form-check-label" for="includeSummary">
                                                        تضمين الملخص التنفيذي
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="includeDetails" checked>
                                                    <label class="form-check-label" for="includeDetails">
                                                        تضمين التفاصيل الكاملة
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="autoOpenReports">
                                                    <label class="form-check-label" for="autoOpenReports">
                                                        فتح التقارير تلقائياً بعد الإنشاء
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="saveReportHistory" checked>
                                                    <label class="form-check-label" for="saveReportHistory">
                                                        حفظ تاريخ التقارير
                                                    </label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="compressReports">
                                                    <label class="form-check-label" for="compressReports">
                                                        ضغط التقارير الكبيرة
                                                    </label>
                                                </div>
                                            </div>

                                            <!-- Save Button -->
                                            <div class="col-12">
                                                <hr>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="icon">💾</i> حفظ إعدادات التقارير
                                                </button>
                                                <button type="button" class="btn btn-secondary ms-2" id="resetReportsSettings">
                                                    <i class="icon">🔄</i> إعادة تعيين
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                                <!-- System Information Tab -->
                                <div class="tab-pane fade" id="system-info" role="tabpanel">
                                    <h5 class="mb-4">معلومات النظام</h5>

                                    <div class="row g-4">
                                        <!-- System Information -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">معلومات التطبيق</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="info-item">
                                                        <strong>اسم التطبيق:</strong> <span class="text-primary fw-bold">تصفية برو</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>الإصدار:</strong> 1.0.0
                                                    </div>

                                                    <div class="info-item">
                                                        <strong>المطور:</strong> <span class="text-success fw-bold">محمد أمين الكامل</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>آخر تحديث:</strong> <span id="lastUpdateDate">جاري التحميل...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">معلومات تقنية</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="info-item">
                                                        <strong>قاعدة البيانات:</strong> SQLite
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>التقنية:</strong> Electron + JavaScript
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>إصدار Node.js:</strong> <span id="nodeVersion">جاري التحميل...</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>إصدار Electron:</strong> <span id="electronVersion">جاري التحميل...</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>نظام التشغيل:</strong> <span id="osInfo">جاري التحميل...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Performance Info -->
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">معلومات الأداء</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <h5 class="text-primary" id="memoryUsage">جاري الحساب...</h5>
                                                                <small class="text-muted">استخدام الذاكرة</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <h5 class="text-success" id="cpuUsage">جاري الحساب...</h5>
                                                                <small class="text-muted">استخدام المعالج</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <h5 class="text-info" id="uptime">جاري الحساب...</h5>
                                                                <small class="text-muted">مدة التشغيل</small>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="text-center">
                                                                <h5 class="text-warning" id="dbConnections">جاري الحساب...</h5>
                                                                <small class="text-muted">اتصالات قاعدة البيانات</small>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- License and Support -->
                                        <div class="col-12">

                                        </div>
                                    </div>
                                </div>

                                <!-- Updates Settings Tab -->
                                <div class="tab-pane fade" id="updates-settings" role="tabpanel">
                                    <h5 class="mb-4">إعدادات التحديثات</h5>

                                    <div class="row g-4">
                                        <!-- Current Version Info -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">الإصدار الحالي</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="info-item">
                                                        <strong>إصدار التطبيق:</strong> <span id="current-app-version" class="text-primary fw-bold">جاري التحميل...</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>تاريخ آخر فحص:</strong> <span id="last-update-check" class="text-muted">لم يتم الفحص بعد</span>
                                                    </div>
                                                    <div class="info-item">
                                                        <strong>حالة التحديث:</strong> <span id="update-status" class="text-success">محدث</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Update Settings -->
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">إعدادات التحديث التلقائي</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="form-check form-switch mb-3">
                                                        <input class="form-check-input" type="checkbox" id="autoUpdateEnabled">
                                                        <label class="form-check-label" for="autoUpdateEnabled">
                                                            تفعيل التحديث التلقائي
                                                        </label>
                                                    </div>
                                                    <small class="text-muted">
                                                        عند التفعيل، سيقوم التطبيق بالبحث عن التحديثات تلقائياً عند بدء التشغيل
                                                    </small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Update Actions -->
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">إجراءات التحديث</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="d-flex gap-3 flex-wrap">
                                                        <button type="button" class="btn btn-primary" id="manual-update-check" onclick="updaterUI.checkForUpdates()">
                                                            <i class="fas fa-search"></i> البحث عن تحديثات
                                                        </button>
                                                        <button type="button" class="btn btn-info" id="view-changelog" onclick="showChangelogModal()">
                                                            <i class="fas fa-list"></i> عرض سجل التغييرات
                                                        </button>
                                                        <button type="button" class="btn btn-secondary" id="update-settings-save" onclick="saveUpdateSettings()">
                                                            <i class="fas fa-save"></i> حفظ الإعدادات
                                                        </button>
                                                    </div>

                                                    <div class="mt-3">
                                                        <div id="update-status-message" class="alert alert-info d-none" role="alert">
                                                            <i class="fas fa-info-circle"></i>
                                                            <span id="update-status-text">جاري البحث عن التحديثات...</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Update History -->
                                        <div class="col-12">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="mb-0">سجل التحديثات</h6>
                                                </div>
                                                <div class="card-body">
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>الإصدار</th>
                                                                    <th>التاريخ</th>
                                                                    <th>الوصف</th>
                                                                    <th>الحالة</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="update-history-table">
                                                                <tr>
                                                                    <td>2.0.0</td>
                                                                    <td>2025-01-23</td>
                                                                    <td>إضافة نظام التحديث التلقائي وتحسينات عامة</td>
                                                                    <td><span class="badge bg-success">مثبت</span></td>
                                                                </tr>
                                                                <tr>
                                                                    <td>1.0.0</td>
                                                                    <td>2025-01-01</td>
                                                                    <td>الإصدار الأولي من التطبيق</td>
                                                                    <td><span class="badge bg-primary">أساسي</span></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>


    </div>



    <!-- Advanced Print Options Modal -->
    <div class="modal fade" id="printOptionsModal" tabindex="-1" aria-labelledby="printOptionsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printOptionsModalLabel">خيارات الطباعة المتقدمة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Printer Selection -->
                        <div class="col-md-6 mb-3">
                            <label for="printerSelect" class="form-label">اختيار الطابعة</label>
                            <select class="form-select" id="printerSelect">
                                <option value="">جاري تحميل الطابعات...</option>
                            </select>
                        </div>

                        <!-- Number of Copies -->
                        <div class="col-md-6 mb-3">
                            <label for="copiesInput" class="form-label">عدد النسخ</label>
                            <input type="number" class="form-control" id="copiesInput" value="1" min="1" max="10">
                        </div>

                        <!-- Paper Size -->
                        <div class="col-md-6 mb-3">
                            <label for="paperSizeSelect" class="form-label">حجم الورق</label>
                            <select class="form-select" id="paperSizeSelect">
                                <option value="A4">A4</option>
                                <option value="A3">A3</option>
                                <option value="Letter">Letter</option>
                                <option value="Legal">Legal</option>
                            </select>
                        </div>

                        <!-- Orientation -->
                        <div class="col-md-6 mb-3">
                            <label for="orientationSelect" class="form-label">اتجاه الورق</label>
                            <select class="form-select" id="orientationSelect">
                                <option value="portrait">عمودي</option>
                                <option value="landscape">أفقي</option>
                            </select>
                        </div>

                        <!-- Color -->
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="colorPrintCheck">
                                <label class="form-check-label" for="colorPrintCheck">
                                    طباعة ملونة
                                </label>
                            </div>
                        </div>

                        <!-- Duplex -->
                        <div class="col-md-6 mb-3">
                            <label for="duplexSelect" class="form-label">الطباعة على الوجهين</label>
                            <select class="form-select" id="duplexSelect">
                                <option value="simplex">وجه واحد</option>
                                <option value="longEdge">وجهين - الحافة الطويلة</option>
                                <option value="shortEdge">وجهين - الحافة القصيرة</option>
                            </select>
                        </div>

                        <!-- Margins -->
                        <div class="col-12 mb-3">
                            <label class="form-label">الهوامش (سم)</label>
                            <div class="row">
                                <div class="col-3">
                                    <label for="marginTop" class="form-label">أعلى</label>
                                    <input type="number" class="form-control" id="marginTop" value="1" min="0" max="5" step="0.1">
                                </div>
                                <div class="col-3">
                                    <label for="marginRight" class="form-label">يمين</label>
                                    <input type="number" class="form-control" id="marginRight" value="1" min="0" max="5" step="0.1">
                                </div>
                                <div class="col-3">
                                    <label for="marginBottom" class="form-label">أسفل</label>
                                    <input type="number" class="form-control" id="marginBottom" value="1" min="0" max="5" step="0.1">
                                </div>
                                <div class="col-3">
                                    <label for="marginLeft" class="form-label">يسار</label>
                                    <input type="number" class="form-control" id="marginLeft" value="1" min="0" max="5" step="0.1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-info" id="previewPrintBtn">معاينة</button>
                    <button type="button" class="btn btn-primary" id="directPrintBtn">طباعة مباشرة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="../node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

    <!-- SweetAlert2 JS -->
    <script src="../node_modules/sweetalert2/dist/sweetalert2.all.min.js"></script>

    <!-- Dialog Utilities -->
    <script src="dialog-utils.js"></script>

    <!-- Environment Manager -->
    <script src="environment.js"></script>

    <!-- Development Scripts - Only load in development mode -->
    <script>
        // Wait for environment manager to be loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Use the environment manager for reliable environment detection
            const shouldLoadTests = window.EnvironmentManager &&
                                   window.EnvironmentManager.shouldLoadTestScripts();

            if (shouldLoadTests) {
            const testScripts = [
                'dialog-test.js',
                'customer-receipts-test.js',
                'suppliers-test.js',
                'new-features-test.js',
                'bug-fixes-test.js',
                'advanced-bug-fixes-test.js',
                'edit-reconciliation-diagnostic.js',
                'direct-edit-test.js',
                'reconciliation-debug-test.js',
                'advanced-print-system-test.js',
                'bank-receipts-fix-test.js',
                'edit-data-loading-diagnostic.js',
                'quick-edit-test.js',
                'final-edit-verification.js',
                'save-and-calculation-test.js',
                'export-print-test.js',
                'pdf-export-test.js',
                'filter-save-button-fix-test.js',
                'test-functions.js'
            ];

            let loadedScripts = 0;
            let failedScripts = 0;

            testScripts.forEach(script => {
                try {
                    const scriptElement = document.createElement('script');
                    scriptElement.src = script;
                    scriptElement.onload = () => {
                        loadedScripts++;
                        console.log(`✅ Test script loaded: ${script}`);
                    };
                    scriptElement.onerror = () => {
                        failedScripts++;
                        console.warn(`⚠️ Failed to load test script: ${script}`);
                    };
                    document.head.appendChild(scriptElement);
                } catch (error) {
                    failedScripts++;
                    console.error(`❌ Error loading test script ${script}:`, error);
                }
            });

                // Log summary after a short delay
                setTimeout(() => {
                    console.log(`🧪 Development mode: ${loadedScripts} test scripts loaded, ${failedScripts} failed`);

                    // Log environment configuration
                    if (window.EnvironmentManager) {
                        const config = window.EnvironmentManager.getConfig();
                        console.log('🌍 Environment Config:', config);
                    }
                }, 1000);

            } else {
                const reason = window.EnvironmentManager ?
                    `Environment: ${window.EnvironmentManager.environment}` :
                    'Environment manager not available';
                console.log(`🔒 Production mode: Test scripts skipped for performance optimization (${reason})`);
            }
        });
    </script>

    <!-- Auto-Updater UI -->
    <script src="updater-ui.js"></script>

    <!-- Main Application -->
    <script src="app.js"></script>
</body>
</html>
