"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenericDifferentialDownloader = void 0;
const DifferentialDownloader_1 = require("./DifferentialDownloader");
class GenericDifferentialDownloader extends DifferentialDownloader_1.DifferentialDownloader {
    download(oldBlockMap, newBlockMap) {
        return this.doDownload(oldBlockMap, newBlockMap);
    }
}
exports.GenericDifferentialDownloader = GenericDifferentialDownloader;
//# sourceMappingURL=GenericDifferentialDownloader.js.map