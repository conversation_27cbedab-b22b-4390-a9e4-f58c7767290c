{"version": 3, "file": "BitbucketProvider.js", "sourceRoot": "", "sources": ["../../src/providers/BitbucketProvider.ts"], "names": [], "mappings": ";;;AAAA,+DAAgG;AAGhG,kCAAwE;AACxE,yCAA4F;AAE5F,MAAa,iBAAkB,SAAQ,mBAAoB;IAGzD,YACmB,aAA+B,EAC/B,OAAmB,EACpC,cAAsC;QAEtC,KAAK,CAAC;YACJ,GAAG,cAAc;YACjB,yBAAyB,EAAE,KAAK;SACjC,CAAC,CAAA;QAPe,kBAAa,GAAb,aAAa,CAAkB;QAC/B,YAAO,GAAP,OAAO,CAAY;QAOpC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,aAAa,CAAA;QACrC,IAAI,CAAC,OAAO,GAAG,IAAA,iBAAU,EAAC,8CAA8C,KAAK,IAAI,IAAI,YAAY,CAAC,CAAA;IACpG,CAAC;IAED,IAAY,OAAO;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,QAAQ,CAAA;IACvE,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,iBAAiB,GAAG,IAAI,wCAAiB,EAAE,CAAA;QACjD,MAAM,WAAW,GAAG,IAAA,yBAAkB,EAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;QAC/E,MAAM,UAAU,GAAG,IAAA,qBAAc,EAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC5F,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,SAAS,EAAE,iBAAiB,CAAC,CAAA;YACnF,OAAO,IAAA,0BAAe,EAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;QAC7D,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,IAAA,+BAAQ,EAAC,oCAAoC,IAAI,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,sCAAsC,CAAC,CAAA;QACtK,CAAC;IACH,CAAC;IAED,YAAY,CAAC,UAAsB;QACjC,OAAO,IAAA,uBAAY,EAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAC/C,CAAC;IAED,QAAQ;QACN,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa,CAAA;QAC1C,OAAO,qBAAqB,KAAK,WAAW,IAAI,cAAc,IAAI,CAAC,OAAO,GAAG,CAAA;IAC/E,CAAC;CACF;AAxCD,8CAwCC", "sourcesContent": ["import { CancellationToken, BitbucketOptions, newError, UpdateInfo } from \"builder-util-runtime\"\nimport { AppUpdater } from \"../AppUpdater\"\nimport { ResolvedUpdateFileInfo } from \"../types\"\nimport { getChannelFilename, newBaseUrl, newUrlFromBase } from \"../util\"\nimport { parseUpdateInfo, Provider, ProviderRuntimeOptions, resolveFiles } from \"./Provider\"\n\nexport class BitbucketProvider extends Provider<UpdateInfo> {\n  private readonly baseUrl: URL\n\n  constructor(\n    private readonly configuration: BitbucketOptions,\n    private readonly updater: AppUpdater,\n    runtimeOptions: ProviderRuntimeOptions\n  ) {\n    super({\n      ...runtimeOptions,\n      isUseMultipleRangeRequest: false,\n    })\n    const { owner, slug } = configuration\n    this.baseUrl = newBaseUrl(`https://api.bitbucket.org/2.0/repositories/${owner}/${slug}/downloads`)\n  }\n\n  private get channel(): string {\n    return this.updater.channel || this.configuration.channel || \"latest\"\n  }\n\n  async getLatestVersion(): Promise<UpdateInfo> {\n    const cancellationToken = new CancellationToken()\n    const channelFile = getChannelFilename(this.getCustomChannelName(this.channel))\n    const channelUrl = newUrlFromBase(channelFile, this.baseUrl, this.updater.isAddNoCacheQuery)\n    try {\n      const updateInfo = await this.httpRequest(channelUrl, undefined, cancellationToken)\n      return parseUpdateInfo(updateInfo, channelFile, channelUrl)\n    } catch (e: any) {\n      throw newError(`Unable to find latest version on ${this.toString()}, please ensure release exists: ${e.stack || e.message}`, \"ERR_UPDATER_LATEST_VERSION_NOT_FOUND\")\n    }\n  }\n\n  resolveFiles(updateInfo: UpdateInfo): Array<ResolvedUpdateFileInfo> {\n    return resolveFiles(updateInfo, this.baseUrl)\n  }\n\n  toString() {\n    const { owner, slug } = this.configuration\n    return `Bitbucket (owner: ${owner}, slug: ${slug}, channel: ${this.channel})`\n  }\n}\n"]}