<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التحديث - Updater Test</title>
    <link href="node_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-body {
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            background: #f8f9fa;
        }
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🔄 اختبار نظام التحديث التلقائي</h1>
            <p>Auto-Updater System Test</p>
        </div>
        
        <div class="test-body">
            <!-- Test 1: Check if electron-updater is installed -->
            <div class="test-section">
                <h4>📦 اختبار 1: فحص تثبيت electron-updater</h4>
                <p>التحقق من وجود مكتبة electron-updater في التبعيات</p>
                <button class="btn btn-primary" onclick="testElectronUpdaterInstallation()">تشغيل الاختبار</button>
                <div id="test1-result" class="test-result d-none"></div>
            </div>

            <!-- Test 2: Check package.json configuration -->
            <div class="test-section">
                <h4>⚙️ اختبار 2: فحص إعدادات package.json</h4>
                <p>التحقق من إعدادات النشر والتحديث في package.json</p>
                <button class="btn btn-primary" onclick="testPackageJsonConfig()">تشغيل الاختبار</button>
                <div id="test2-result" class="test-result d-none"></div>
            </div>

            <!-- Test 3: Check main.js integration -->
            <div class="test-section">
                <h4>🔧 اختبار 3: فحص تكامل main.js</h4>
                <p>التحقق من إضافة كود التحديث في main.js</p>
                <button class="btn btn-primary" onclick="testMainJsIntegration()">تشغيل الاختبار</button>
                <div id="test3-result" class="test-result d-none"></div>
            </div>

            <!-- Test 4: Check UI components -->
            <div class="test-section">
                <h4>🎨 اختبار 4: فحص مكونات واجهة المستخدم</h4>
                <p>التحقق من وجود عناصر واجهة التحديث</p>
                <button class="btn btn-primary" onclick="testUIComponents()">تشغيل الاختبار</button>
                <div id="test4-result" class="test-result d-none"></div>
            </div>

            <!-- Test 5: Test update functions -->
            <div class="test-section">
                <h4>🚀 اختبار 5: اختبار وظائف التحديث</h4>
                <p>اختبار الدوال الأساسية لنظام التحديث</p>
                <button class="btn btn-primary" onclick="testUpdateFunctions()">تشغيل الاختبار</button>
                <div id="test5-result" class="test-result d-none"></div>
            </div>

            <!-- Run All Tests -->
            <div class="text-center mt-4">
                <button class="btn btn-success btn-lg" onclick="runAllTests()">
                    🧪 تشغيل جميع الاختبارات
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test 1: Check electron-updater installation
        async function testElectronUpdaterInstallation() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.classList.remove('d-none');
            
            try {
                const fs = require('fs');
                const path = require('path');
                
                // Check package.json dependencies
                const packageJsonPath = path.join(__dirname, 'package.json');
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                
                const hasElectronUpdater = packageJson.dependencies && packageJson.dependencies['electron-updater'];
                
                if (hasElectronUpdater) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ نجح الاختبار: electron-updater مثبت (الإصدار: ${packageJson.dependencies['electron-updater']})`;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ فشل الاختبار: electron-updater غير مثبت';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
            }
        }

        // Test 2: Check package.json configuration
        async function testPackageJsonConfig() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.classList.remove('d-none');
            
            try {
                const fs = require('fs');
                const path = require('path');
                
                const packageJsonPath = path.join(__dirname, 'package.json');
                const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
                
                const hasPublishConfig = packageJson.build && packageJson.build.publish;
                
                if (hasPublishConfig) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ نجح الاختبار: إعدادات النشر موجودة<br>Provider: ${packageJson.build.publish.provider}`;
                } else {
                    resultDiv.className = 'test-result warning';
                    resultDiv.innerHTML = '⚠️ تحذير: إعدادات النشر غير موجودة في package.json';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
            }
        }

        // Test 3: Check main.js integration
        async function testMainJsIntegration() {
            const resultDiv = document.getElementById('test3-result');
            resultDiv.classList.remove('d-none');
            
            try {
                const fs = require('fs');
                const path = require('path');
                
                const mainJsPath = path.join(__dirname, 'src', 'main.js');
                const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
                
                const hasAutoUpdaterImport = mainJsContent.includes('electron-updater');
                const hasUpdateHandlers = mainJsContent.includes('check-for-updates');
                
                if (hasAutoUpdaterImport && hasUpdateHandlers) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ نجح الاختبار: كود التحديث مدمج في main.js';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ فشل الاختبار: كود التحديث غير مدمج بشكل صحيح';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
            }
        }

        // Test 4: Check UI components
        async function testUIComponents() {
            const resultDiv = document.getElementById('test4-result');
            resultDiv.classList.remove('d-none');
            
            try {
                const fs = require('fs');
                const path = require('path');
                
                const updaterUIPath = path.join(__dirname, 'src', 'updater-ui.js');
                const indexHtmlPath = path.join(__dirname, 'src', 'index.html');
                
                const hasUpdaterUI = fs.existsSync(updaterUIPath);
                const indexHtmlContent = fs.readFileSync(indexHtmlPath, 'utf8');
                const hasUpdatesTab = indexHtmlContent.includes('updates-settings');
                
                if (hasUpdaterUI && hasUpdatesTab) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ نجح الاختبار: مكونات واجهة التحديث موجودة';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ فشل الاختبار: مكونات واجهة التحديث مفقودة';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
            }
        }

        // Test 5: Test update functions
        async function testUpdateFunctions() {
            const resultDiv = document.getElementById('test5-result');
            resultDiv.classList.remove('d-none');
            
            try {
                const fs = require('fs');
                const path = require('path');
                
                const appJsPath = path.join(__dirname, 'src', 'app.js');
                const appJsContent = fs.readFileSync(appJsPath, 'utf8');
                
                const hasElectronAPI = appJsContent.includes('window.electronAPI');
                const hasUpdateFunctions = appJsContent.includes('initializeUpdateSystem');
                
                if (hasElectronAPI && hasUpdateFunctions) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ نجح الاختبار: دوال التحديث موجودة في app.js';
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ فشل الاختبار: دوال التحديث مفقودة';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ خطأ في الاختبار: ${error.message}`;
            }
        }

        // Run all tests
        async function runAllTests() {
            console.log('🧪 Running all updater tests...');
            
            await testElectronUpdaterInstallation();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPackageJsonConfig();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMainJsIntegration();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUIComponents();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUpdateFunctions();
            
            console.log('✅ All tests completed');
        }
    </script>
</body>
</html>
