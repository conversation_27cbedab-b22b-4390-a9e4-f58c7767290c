{"version": 3, "file": "KeygenProvider.js", "sourceRoot": "", "sources": ["../../src/providers/KeygenProvider.ts"], "names": [], "mappings": ";;;AAAA,+DAA6F;AAG7F,kCAAwE;AACxE,yCAA4F;AAE5F,MAAa,cAAe,SAAQ,mBAAoB;IAItD,YACmB,aAA4B,EAC5B,OAAmB,EACpC,cAAsC;QAEtC,KAAK,CAAC;YACJ,GAAG,cAAc;YACjB,yBAAyB,EAAE,KAAK;SACjC,CAAC,CAAA;QAPe,kBAAa,GAAb,aAAa,CAAe;QAC5B,YAAO,GAAP,OAAO,CAAY;QALrB,oBAAe,GAAG,eAAe,CAAA;QAYhD,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAA;QAC5D,IAAI,CAAC,OAAO,GAAG,IAAA,iBAAU,EAAC,WAAW,IAAI,gBAAgB,IAAI,CAAC,aAAa,CAAC,OAAO,sBAAsB,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC,CAAA;IACxI,CAAC;IAED,IAAY,OAAO;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,QAAQ,CAAA;IACvE,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,MAAM,iBAAiB,GAAG,IAAI,wCAAiB,EAAE,CAAA;QACjD,MAAM,WAAW,GAAG,IAAA,yBAAkB,EAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;QAC/E,MAAM,UAAU,GAAG,IAAA,qBAAc,EAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;QAC5F,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,WAAW,CACvC,UAAU,EACV;gBACE,MAAM,EAAE,0BAA0B;gBAClC,gBAAgB,EAAE,KAAK;aACxB,EACD,iBAAiB,CAClB,CAAA;YACD,OAAO,IAAA,0BAAe,EAAC,UAAU,EAAE,WAAW,EAAE,UAAU,CAAC,CAAA;QAC7D,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,IAAA,+BAAQ,EAAC,oCAAoC,IAAI,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,sCAAsC,CAAC,CAAA;QACtK,CAAC;IACH,CAAC;IAED,YAAY,CAAC,UAAsB;QACjC,OAAO,IAAA,uBAAY,EAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IAC/C,CAAC;IAED,QAAQ;QACN,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,aAAa,CAAA;QACzD,OAAO,oBAAoB,OAAO,cAAc,OAAO,eAAe,QAAQ,cAAc,IAAI,CAAC,OAAO,GAAG,CAAA;IAC7G,CAAC;CACF;AAhDD,wCAgDC", "sourcesContent": ["import { CancellationToken, KeygenOptions, newError, UpdateInfo } from \"builder-util-runtime\"\nimport { AppUpdater } from \"../AppUpdater\"\nimport { ResolvedUpdateFileInfo } from \"../types\"\nimport { getChannelFilename, newBaseUrl, newUrlFromBase } from \"../util\"\nimport { parseUpdateInfo, Provider, ProviderRuntimeOptions, resolveFiles } from \"./Provider\"\n\nexport class KeygenProvider extends Provider<UpdateInfo> {\n  private readonly defaultHostname = \"api.keygen.sh\"\n  private readonly baseUrl: URL\n\n  constructor(\n    private readonly configuration: KeygenOptions,\n    private readonly updater: AppUpdater,\n    runtimeOptions: ProviderRuntimeOptions\n  ) {\n    super({\n      ...runtimeOptions,\n      isUseMultipleRangeRequest: false,\n    })\n    const host = this.configuration.host || this.defaultHostname\n    this.baseUrl = newBaseUrl(`https://${host}/v1/accounts/${this.configuration.account}/artifacts?product=${this.configuration.product}`)\n  }\n\n  private get channel(): string {\n    return this.updater.channel || this.configuration.channel || \"stable\"\n  }\n\n  async getLatestVersion(): Promise<UpdateInfo> {\n    const cancellationToken = new CancellationToken()\n    const channelFile = getChannelFilename(this.getCustomChannelName(this.channel))\n    const channelUrl = newUrlFromBase(channelFile, this.baseUrl, this.updater.isAddNoCacheQuery)\n    try {\n      const updateInfo = await this.httpRequest(\n        channelUrl,\n        {\n          Accept: \"application/vnd.api+json\",\n          \"Keygen-Version\": \"1.1\",\n        },\n        cancellationToken\n      )\n      return parseUpdateInfo(updateInfo, channelFile, channelUrl)\n    } catch (e: any) {\n      throw newError(`Unable to find latest version on ${this.toString()}, please ensure release exists: ${e.stack || e.message}`, \"ERR_UPDATER_LATEST_VERSION_NOT_FOUND\")\n    }\n  }\n\n  resolveFiles(updateInfo: UpdateInfo): Array<ResolvedUpdateFileInfo> {\n    return resolveFiles(updateInfo, this.baseUrl)\n  }\n\n  toString() {\n    const { account, product, platform } = this.configuration\n    return `Keygen (account: ${account}, product: ${product}, platform: ${platform}, channel: ${this.channel})`\n  }\n}\n"]}